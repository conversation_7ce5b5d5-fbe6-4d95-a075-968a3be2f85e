<krpano debugmode="true">

<!-- <autorotate enabled="true"
            waittime="2.5"
            accel="1.0"
            speed="0.40"
            horizon="0.0"
            tofov="on"
            oneroundrange="360.0"
            zoomslowdown="true"
            interruptionevents="userviewchange|layers|keyboard"
            />


		<action name="startup" autorun="onstart">
		if(startscene === null OR !scene[get(startscene)], copy(startscene,scene[0].name); );
		loadscene(get(startscene), null, MERGE);
		if(startactions !== null, startactions() );

		</action>
	
		<action name="startView">
		tween(view.hlookat,-1.0,4,,
			tween(view.fov,140,2);
			tween(view.vlookat,40, 3);
			tween(view.hlookat,-1.0,1,,
					tween(view.fov,120, 2);
					tween(view.distortion,0,2,,
					tween(layer[contrlInfo].alpha,1,1);
					);
				);

			);	
		</action> -->
 

    <!-- <include url="../shanghai/scene_level_thief.xml" /> -->
    <include url="../shanghai/tour2.xml" />
    <scene name="scene_level_1" title="寻物游戏场景 - 等级 1" autoload="true" thumburl="panos/big-0519-0712-plaza.tiles/thumb.jpg"  onviewchange="hotspot_distance_manager();">
        <control bouncinglimits="calc:image.cube ? true : false" />
        <view hlookat="0.0" vlookat="0.0" fovtype="MFOV" fov="120" maxpixelzoom="2.0" fovmin="70" fovmax="140" limitview="auto" />
     
        <!-- <preview url="/panos/big-0519-0712-plaza.tiles/preview.jpg" />

		<image>
			<sphere url="/panos/big-0519-0712-plaza.tiles/l%l/%00v/l%l_%00v_%00h.jpg" multires="512,2048x1026,3840x1920,7680x3840,15104x7552,30336x15168,60672x30336,121600x60800" />
		</image> -->

        <!-- 小偷热点 -->
        <hotspot name="thief_11"
                 url="/img/thief/thief_1_320/thief1_1.png"
                 ath="30"
                 atv="15"
                 scale="0.8"
                 onclick="js(onThiefClicked('thief_11'));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot>
         <hotspot name="thief_111"
                 url="/img/thief/thief_1_320/thief1_1.png"
                 ath="-80"
                 atv="35"
                 scale="0.8"
                 onclick="js(onThiefClicked('thief_111'));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot>
        <hotspot name="thief_20"
                 url="/img/thief/thief_1_320/thief1_2.png"
                 ath="15"
                 atv="25"
                 scale="0.8"
                 onclick="js(onThiefClicked('thief_20'));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot>
        <hotspot name="thief_32"
                 url="/img/thief/thief_1_320/thief1_3.png"
                 ath="15"
                 atv="10"
                 scale="0.8"
                 onclick="js(onThiefClicked('thief_32'));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot>
        <hotspot name="thief_14"
                 url="/img/thief/thief_1_320/thief1_4.png"
                 ath="50"
                 atv="25"
                 scale="0.7"
                 onclick="js(onThiefClicked('thief_14'));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.7" type="thief" />
        </hotspot>
        <hotspot name="thief_55"
                 url="/img/thief/thief_1_320/thief1_5.png"
                 ath="15"
                 atv="15"
                 scale="0.8"
                 onclick="js(onThiefClicked('thief_55'));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot>
         <hotspot name="thief_16"
                 url="/img/thief/thief_1_320/thief1_5.png"
                 ath="12"
                 atv="5"
                 scale="0.8"
                 onclick="js(onThiefClicked(get(name)));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot>
         <hotspot name="thief_7"
                 url="/img/thief/thief_1_320/thief1_5.png"
                 ath="3"
                 atv="40"
                 scale="0.8"
                 onclick="js(onThiefClicked(get(name)));"
                 visible="false"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.8" type="thief" />
        </hotspot> -->
        
        

        <!-- 宝箱热点 -->
        <!-- <hotspot name="treasure_1"
                 url="/img/gold.png"
                 ath="90"
                 atv="-8"
                 scale="0.1"
                 onclick="js(onTreasureClicked('treasure_1'));"
                 visible="true"
                 distorted="true"
                 renderer="webgl"
                 enabled="true"
                 alpha="1.0"
                 zorder="1">
            <userData managed="true" baseScale="0.04" type="treasure" />
        </hotspot> -->
        

    
    </scene>

    <!-- LOD管理系统 - 热点距离和缩放管理 -->
    <action name="setup_hotspot_lod_events">
        trace('初始化小偷场景LOD管理系统');
        
        <!-- 设置LOD配置参数 -->
        set(lod_config.max_visible_distance, 120);
        set(lod_config.very_near_threshold, 25);
        set(lod_config.near_threshold, 45);
        set(lod_config.mid_threshold, 70);
        set(lod_config.far_threshold, 95);
        
        <!-- 设置缩放级别 - 小偷热点专用 -->
        set(lod_scales.very_near, 1.5);
        set(lod_scales.near, 1.2);
        set(lod_scales.normal, 1.0);
        set(lod_scales.mid, 0.8);
        set(lod_scales.far, 0.6);
        set(lod_scales.very_far, 0.4);
        
        <!-- 可见性阈值 -->
        set(lod_config.fade_start, 100);
        set(lod_config.fade_end, 120);
        
        <!-- 延迟初始化热点LOD状态 -->
        <!-- delayedcall(0.1, init_hotspot_lod_state()); -->
        
        <!-- 延迟测试LOD管理器 -->
        delayedcall(1.0, lod_manager());
        
        <!-- 通知JavaScript LOD配置已设置 -->
        <!-- js(console.log('🔍 XML: LOD系统初始化')); -->
        js(window.onLODSystemReady());
        <!-- trace('LOD配置初始化完成'); -->
    </action>

    <!-- 初始化热点LOD状态 -->
    <action name="init_hotspot_lod_state">
        <!-- trace('开始初始化热点LOD状态');
        js(console.log('🎯 XML: 开始初始化热点LOD状态')); -->
        
        <!-- 接收JavaScript传递的热点数据 -->
        if(global_hotspot_data !== null,
            <!-- 使用传递的热点数据初始化LOD状态 -->
            for(set(i, 0), i LT calc(global_hotspot_data.length), inc(i),
                copy(hotspot_name, global_hotspot_data[get(i)].name);
                if(hotspot[get(hotspot_name)].name !== null,
                    set(hotspot[get(hotspot_name)].lod_distance, 0);
                    set(hotspot[get(hotspot_name)].lod_scale, 1.0);
                    <!-- set(hotspot[get(hotspot_name)].onclick, js(onThiefClicked(get(hotspot_name)))); -->
                    <!-- js(console.log('✅ 初始化热点:', get(hotspot_name), '成功访问属性')); -->
                    <!-- trace('初始化热点:', get(hotspot_name));  -->
                ,
                    <!-- js(console.log('❌ 热点不存在:', get(hotspot_name))); -->
                );
            );
        ,
            <!-- 如果没有传递数据，使用默认的硬编码方式（向后兼容） -->
            <!-- js(console.log('⚠️ 未传递热点数据，使用默认初始化方式')); -->
            for(set(i, 1), i LE 100, inc(i),
                txtadd(hotspot_name, 'thief_', get(i));
                if(hotspot[get(hotspot_name)].name !== null,
                    set(hotspot[get(hotspot_name)].lod_distance, 0);
                    set(hotspot[get(hotspot_name)].lod_scale, 1.0);
                    <!-- js(console.log('✅ 初始化热点:', get(hotspot_name), '成功访问属性'));
                    trace('初始化热点:', get(hotspot_name)); -->
                ,
                    js(console.log('❌ 热点不存在:', get(hotspot_name)));
                );
            );
        );
        
        <!-- js(console.log('🎯 XML: 热点LOD状态初始化完成')); -->
        <!-- trace('热点LOD状态初始化完成'); -->
    </action>

    <action name="hotspot_distance_manager">
        
        <!-- 检查视图是否准备就绪 -->
        if(view.hlookat !== null,
            <!-- js(console.log('✅ 视图已准备，开始LOD更新')); -->
            
            <!-- 获取当前视角信息 -->
            copy(current_h, view.hlookat);
            copy(current_v, view.vlookat);
            copy(current_fov, view.fov);
            
            <!-- 验证视角数据有效性 -->
            if(current_h !== null AND current_v !== null AND current_fov !== null,
                <!-- 使用全局热点数据列表 -->
                if(global_hotspot_data !== null,
                    <!-- 处理动态热点数据 -->
                    for(set(i, 0), i LT calc(global_hotspot_data.length), inc(i),
                        copy(hotspot_name, global_hotspot_data[get(i)].name);
                        
                        if(hotspot[get(hotspot_name)].name !== null,
                            <!-- 计算角度距离 -->
                            sub(h_diff, hotspot[get(hotspot_name)].ath, get(current_h));
                            sub(v_diff, hotspot[get(hotspot_name)].atv, get(current_v));
                            
                            <!-- 处理水平角度环绕 -->
                            if(h_diff GT 180, sub(h_diff, h_diff, 360));
                            if(h_diff LT -180, add(h_diff, h_diff, 360));
                            
                            <!-- 计算欧氏距离 -->
                            mul(h_diff_sq, h_diff, h_diff);
                            mul(v_diff_sq, v_diff, v_diff);
                            add(sum_sq, h_diff_sq, v_diff_sq);
                            Math.sqrt(sum_sq, distance);
                            
                            <!-- 存储距离信息 -->
                            set(hotspot[get(hotspot_name)].lod_distance, get(distance));
                            
                            <!-- 获取基础缩放值 -->
                            if(hotspot[get(hotspot_name)].scale !== null,
                                copy(base_scale, hotspot[get(hotspot_name)].scale);
                            ,
                                set(base_scale, 0.05);
                            );
                            
                            <!-- LOD缩放计算 -->
                            if(distance LT lod_config.very_near_threshold,
                                mul(new_scale, base_scale, lod_scales.very_near);
                            ,
                            if(distance LT lod_config.near_threshold,
                                mul(new_scale, base_scale, lod_scales.near);
                            ,
                            if(distance LT lod_config.mid_threshold,
                                mul(new_scale, base_scale, lod_scales.normal);
                            ,
                            if(distance LT lod_config.far_threshold,
                                mul(new_scale, base_scale, lod_scales.mid);
                            ,
                            if(distance LT lod_config.max_visible_distance,
                                mul(new_scale, base_scale, lod_scales.far);
                            ,
                                mul(new_scale, base_scale, lod_scales.very_far);
                            )))));
                            
                            <!-- 应用缩放 -->
                            set(hotspot[get(hotspot_name)].scale, get(new_scale));
                            set(hotspot[get(hotspot_name)].lod_scale, get(new_scale));
                            
                            <!-- 可见性管理 -->
                            if(distance GT lod_config.max_visible_distance,
                                set(hotspot[get(hotspot_name)].visible, false);
                            ,
                                <!-- 根据热点类型设置可见性,名称是thief_开头的热点是不可见的，并且enabled为false -->
                                if(hotspot[get(hotspot_name)].name.startsWith('thief_') AND hotspot[get(hotspot_name)].enabled == false,
                                    set(hotspot[get(hotspot_name)].visible, false);
                                ,
                                    set(hotspot[get(hotspot_name)].visible, true);
                                );
                            );
                            
                            <!-- 调试输出 -->
                            <!-- copy(debug_name, hotspot[get(hotspot_name)].name); -->
                            <!-- copy(debug_visible, hotspot[get(hotspot_name)].visible); -->
                            <!-- js(console.log('🎯 11热点:', get(debug_name), '距离:', get(distance), '缩放:', get(new_scale), '可见:', get(debug_visible))); -->
                        );
                    );
                ,
                    <!-- 如果没有全局数据，使用默认的硬编码方式（向后兼容） -->
                    <!-- js(console.log('⚠️ 未找到全局热点数据，使用默认LOD更新方式')); -->
                    for(set(i, 1), i LE 100, inc(i),
                        txtadd(hotspot_name, 'thief_', get(i));
                        
                        if(hotspot[get(hotspot_name)].name !== null,
                            <!-- 计算角度距离 -->
                            sub(h_diff, hotspot[get(hotspot_name)].ath, get(current_h));
                            sub(v_diff, hotspot[get(hotspot_name)].atv, get(current_v));
                            
                            <!-- 处理水平角度环绕 -->
                            if(h_diff GT 180, sub(h_diff, h_diff, 360));
                            if(h_diff LT -180, add(h_diff, h_diff, 360));
                            
                            <!-- 计算欧氏距离 -->
                            mul(h_diff_sq, h_diff, h_diff);
                            mul(v_diff_sq, v_diff, v_diff);
                            add(sum_sq, h_diff_sq, v_diff_sq);
                            Math.sqrt(sum_sq, distance);
                            
                            <!-- 存储距离信息 -->
                            set(hotspot[get(hotspot_name)].lod_distance, get(distance));
                            
                            <!-- 获取基础缩放值 -->
                            if(hotspot[get(hotspot_name)].scale !== null,
                                copy(base_scale, hotspot[get(hotspot_name)].scale);
                            ,
                                set(base_scale, 0.05);
                            );
                            
                            <!-- LOD缩放计算 -->
                            if(distance LT lod_config.very_near_threshold,
                                mul(new_scale, base_scale, lod_scales.very_near);
                            ,
                            if(distance LT lod_config.near_threshold,
                                mul(new_scale, base_scale, lod_scales.near);
                            ,
                            if(distance LT lod_config.mid_threshold,
                                mul(new_scale, base_scale, lod_scales.normal);
                            ,
                            if(distance LT lod_config.far_threshold,
                                mul(new_scale, base_scale, lod_scales.mid);
                            ,
                            if(distance LT lod_config.max_visible_distance,
                                mul(new_scale, base_scale, lod_scales.far);
                            ,
                                mul(new_scale, base_scale, lod_scales.very_far);
                            )))));
                            
                            <!-- 应用缩放 -->
                            set(hotspot[get(hotspot_name)].scale, get(new_scale));
                            set(hotspot[get(hotspot_name)].lod_scale, get(new_scale));
                            
                            <!-- 可见性管理 -->
                            if(distance GT lod_config.max_visible_distance,
                                set(hotspot[get(hotspot_name)].visible, false);
                            ,
                                <!-- 根据热点类型设置可见性 -->
                                if(hotspot[get(hotspot_name)].userData.type == 'thief',
                                    set(hotspot[get(hotspot_name)].visible, false);
                                ,
                                    set(hotspot[get(hotspot_name)].visible, true);
                                );
                            );
                            
                            <!-- 调试输出 -->
                            copy(debug_name, hotspot[get(hotspot_name)].name);
                            copy(debug_visible, hotspot[get(hotspot_name)].visible);
                            <!-- js(console.log('🎯 热点:', get(debug_name), '距离:', get(distance), '缩放:', get(new_scale), '可见:', get(debug_visible))); -->
                        );
                    );
                );
                
                <!-- js(console.log('✅ LOD更新完成')); -->
                <!-- trace('LOD更新完成'); -->
            ,
                <!-- js(console.log('❌ 视角数据无效')); -->
                <!-- trace('视角数据无效'); -->
            );
        ,
            <!-- js(console.log('❌ 视图未准备好')); -->
            <!-- trace('视图未准备好'); -->
        );
        
        <!-- js(console.log('🔄 LOD更新: hotspot_distance_manager 执行完成')); -->
    </action>

    <!-- LOD管理器 -->
    <action name="lod_manager">
        
        <!-- 检查视图和热点是否就绪 -->
        if(view.hlookat !== null,
            hotspot_distance_manager();
        ,
            <!-- js(console.log('❌ 视图或热点未就绪，延迟1秒重试')); -->
            delayedcall(1.0, lod_manager());
        );
    </action>

    <!-- 获取LOD统计信息 -->
    <action name="get_lod_stats">
        <!-- trace('LOD统计信息:');
        js(console.log('📊 LOD统计信息:')); -->
        
        <!-- 统计变量 -->
        set(visible_count, 0);
        set(hidden_count, 0);
        
        <!-- 遍历小偷热点 -->
        for(set(i, 1), i LE 10, inc(i),
            <!-- txtadd(h_name, 'thief_', get(i)); -->
            if(hotspot[get(h_name)].name !== null,
                <!-- txtadd(stat_msg, '热点 ', get(h_name), ' 距离=', hotspot[get(h_name)].lod_distance, ' 可见=', hotspot[get(h_name)].visible); -->
                trace(get(stat_msg));
                js(get(stat_msg));
                
                <!-- 统计可见性 -->
                if(hotspot[get(h_name)].visible,
                    inc(visible_count);
                ,
                    inc(hidden_count);
                );
            );
        );
        
        <!-- txtadd(summary, '统计: 可见=', get(visible_count), ' 隐藏=', get(hidden_count)); -->
        trace(get(summary));
        js(get(summary));
    </action>
</krpano>