<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/img/radar_yuan.33a149dc.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <!-- 解决Google登录跨域问题 -->
    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin-allow-popups">
    <meta http-equiv="Cross-Origin-Embedder-Policy" content="unsafe-none">
    
    <!-- 解决图片跨域问题 -->
    <meta http-equiv="Cross-Origin-Resource-Policy" content="cross-origin">
    
    <title>VR寻物游戏</title>
    
    <!-- 提前设置正确的font-size，避免FOUC -->
    <script>
      (function() {
        // 获取屏幕宽度
        var screenWidth = window.innerWidth || document.documentElement.clientWidth;
        var designWidth = 750;
        var rootValue = 75;
        
        // 计算缩放比例
        var scale = screenWidth / designWidth;
        var fontSize = rootValue * scale;
        
        // 限制最值
        if (screenWidth >= 1024) {
          fontSize = Math.min(45, fontSize);
        } else if (screenWidth <= 480) {
          fontSize = Math.max(32, fontSize);
        } else if (screenWidth <= 768) {
          fontSize = Math.max(48, fontSize);
        } else if (screenWidth < 1024) {
          fontSize = Math.min(64, fontSize);
        }
        
        // 立即设置font-size
        document.documentElement.style.fontSize = fontSize + 'px';
        // console.log('Pre-calculated font-size:', fontSize + 'px');
      })();
    </script>
    
    <!-- 加载遮罩样式 -->
    <style>
      /* #app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }
      
      #app-loading.hidden {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 16px;
        text-align: center;
      } */
    </style>
    
    <!-- 
      Load krpano.js from the public directory. 
      Ensure krpano.js is in your krpano-vue-game/public/ folder.
    -->
    <script src="/tour.js"></script>
    <script src="https://sdk.crazygames.com/crazygames-sdk-v3.js"></script>
   <!-- 引入 Google Identity Services 客户端库 -->
   <script src="https://accounts.google.com/gsi/client" async defer></script>
   <!-- <script src="/scripts/google_gsi_client.js"></script> -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <!-- Sign in with Apple -->
    <meta name="appleid-signin-client-id" content="dsd3039fd81811555-kgc17fddfgbtqvtbh8pc9hd1nl97nd63nq03.apps.googleusercontent.com">
    <meta name="appleid-signin-scope" content="name email">
    <meta name="appleid-signin-redirect-uri" content="YOUR_APPLE_REDIRECT_URI">
    <meta name="appleid-signin-state" content="[STATE_VALUE]"> <!-- Optional: for CSRF protection, generate and validate server-side -->
    <meta name="appleid-signin-use-popup" content="true"> <!-- Use true for popup UX -->
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js" async defer></script>
  </head>
  <body>
    <!-- 加载遮罩 -->
    <!-- <div id="app-loading">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载VR城市寻宝游戏...</div>
      </div>
    </div> -->
    
    <!-- 广告容器 -->
  <div id="ad-container"></div>
    <div id="fb-root"></div>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>
  </body>
</html>
