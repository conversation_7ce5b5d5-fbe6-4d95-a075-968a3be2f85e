export default {
  common: {
    confirm: 'Confirmer',
    cancel: 'Annuler',
    close: 'Fermer',
    loading: 'Chargement...',
    tips: 'Conseils',
    warning: 'Avertissement',
    success: 'Succès',
    error: 'Erreur',
    gold: 'Or',
    diamond: 'Diamant',
    hour: 'heure',
    hours: 'heures',
    minutes: 'minutes',
    details: 'Détails',
    upgrade: 'Améliorer',
    purchase: 'Acheter',
    level: 'Niveau'
  },
  ranking: {
    title: 'Classement',
    rank: 'Rang',
    name: 'Nom',
    gold: 'Or',
    times: 'Temps',
  },
  home: {
    title: 'Chasse au trésor en ville',
    subtitle: 'Explorez de magnifiques paysages dans le monde entier et trouvez des trésors cachés',
    startGame: 'Commencer le jeu',
    quickLogin: 'Connexion rapide',
    loginWith: {
      google: 'Se connecter avec Google',
      facebook: 'Se connecter avec Facebook',
      apple: 'Se connecter avec Apple',
      wechat: 'Se connecter avec WeChat'
    },
    loginDeveloping: 'La connexion {platform} est en cours de développement...'
  },
  Tasks: 'Tâches',
  game: {
    attack: 'Attaquer',
    level: 'Niv.{level}',
    coins: 'Pièces',
    time: 'Temps',
    map: 'Carte',
    ranking: 'Classement',
    exp: 'Expérience',
    lottery: {
      title: 'Roulette',
    },
    tools: {
      radar: 'Radar',
      magnifier: 'Loupe',
      cooldown: '{seconds}s',
      watchAdForUse: 'Regarder la pub pour utiliser',
      radarCooldownTip: 'Le radar est en recharge ou hors d\'usage',
      magnifierCooldownTip: 'La loupe est en recharge ou hors d\'usage',
      radarScanned: 'Radar a détecté une cible !',
      magnifierUsed: 'Les voleurs proches ont été agrandis !',
      noThievesNearby: 'Aucun voleur à proximité',
      loadingAd: 'Chargement de la publicité...',
      gotRadar: '+1 utilisation Radar',
      gotMagnifier: '+1 utilisation Loupe'
    },

    rewards: {
      caughtThief: 'Voleur attrapé !',
      cleanedGarbage: 'Déchets nettoyés !',
      foundTreasure: 'Trésor trouvé !',
      watchAdForDouble: 'Regarder la pub pour doubler la récompense',
      gotDoubleReward: 'Récompense doublée +{amount}'
    },

    levelMap: {
      title: 'Carte des niveaux',
      locked: 'Ce niveau est verrouillé',
      currentLevel: 'Déjà dans ce niveau',
      switchToLevel: 'Passer au niveau {name}'
    },

    ranking: {
      title: 'Classement',
      rank: 'Rang',
      name: 'Nom',
      gold: 'Or',
      times: 'Temps',
      you: 'Vous'
    },
    attackFailed: 'Attaque échouée',
    attackSuccess: 'Attaque réussie',
    notEnoughAmmo: 'Munitions insuffisantes'
  },

  cities: {
    rome: 'Rome',
    paris: 'Paris',
    newYork: 'New York',
    shanghai: 'Shanghai',
    prague: 'Prague',
    zurich: 'Zurich',
    iceland: 'Islande',
    venice: 'Venise'
  },

  settings: {
    title: 'Paramètres du jeu',
    language: 'Langue',
    sound: 'Son',
    music: 'Musique',
    vibration: 'Vibration',
    quality: 'Qualité',
    high: 'Haute',
    medium: 'Moyenne',
    low: 'Basse'
  },

  guide: {
    skip: 'Passer',
    prev: 'Précédent',
    next: 'Suivant',
    start: 'Commencer l\'aventure !',
    step1: {
      title: 'Trouvez les vilains voleurs !',
      description: 'Utilisez vos doigts pour zoomer et faire glisser afin de les trouver !'
    },
    step2: {
      title: 'Le roi des déchets spatiaux envahit !',
      description: 'Devenez le gardien de la ville et sauvez le monde !'
    },
    step3: {
      title: 'Collectez des munitions, feu !',
      description: 'Collectez les voleurs et les déchets pour attaquer le Roi !'
    },
    step4: {
      title: 'Améliorez l\'équipement, devenez plus fort !',
      description: 'Êtes-vous prêt ?'
    }
  },

  // 游戏菜单
  gameMenu: {
    title: 'Menu du Jeu',
    cityGuide: 'Guide de la Ville',
    ranking: 'Classement',
    cardView: 'Vue Carte',
    collectionStats: 'Statistiques de Collection',
    contactUs: 'Nous Contacter',
    // 设置项
    music: 'Musique',
    soundEffects: 'Effets Sonores',
    vibration: 'Vibration',
    language: 'Langue',
    // 提示信息
    musicEnabled: 'Musique activée',
    musicDisabled: 'Musique désactivée',
    sfxEnabled: 'Effets sonores activés',
    sfxDisabled: 'Effets sonores désactivés',
    vibrationEnabled: 'Vibration activée',
    vibrationDisabled: 'Vibration désactivée',
    contactDev: 'Fonction de contact en développement...',
    showUI: 'Afficher UI',
    hideUI: 'Masquer UI'
  },
  Collections: 'Cartes',
  cityCollections: {
    title: 'Collections de villes',
    vatican: {
      title: 'Villes de Vatican',
      card1: 'Carte de Vatican 1',
      card2: 'Carte de Vatican 2'
    },
    description: 'Chaque collection culturelle est un trésor culturel de la ville, vous devez répondre à des questions pour obtenir des récompenses riches'
  },
  
  // 被动收益系统
  passiveIncome: {
    title: 'Revenus Passifs',
    offlineTime: 'Temps hors ligne',
    hourlyRate: 'Taux horaire',
    guardianBonus: 'Bonus Gardien',
    collect: 'Collecter les revenus',
    collectWithAd: 'Regarder une pub pour collecter',
    noIncome: 'Aucun revenu à collecter'
  },
  
  // 守护等级系统
  guardian: {
    title: 'Niveau Gardien',
    level: 'Niveau',
    experience: 'Expérience',
    artifactCollection: 'Collection d\'artefacts',
    totalArtifacts: 'Total d\'artefacts',
    unlockedAbilities: 'Capacités débloquées',
    manualUpgrade: 'Amélioration manuelle',
    nextLevelCost: 'Coût du niveau suivant',
    upgradeHistory: 'Historique d\'amélioration',
    loadHistory: 'Charger l\'historique',
    abilities: {
      'auto_collect': 'Collecte automatique des revenus passifs',
      'extended_accumulation': 'Prolonger l\'accumulation à 48 heures',
      'passive_income_boost': 'Bonus de revenus passifs',
      'ad_reward_boost': 'Double récompense publicitaire'
    },
    sources: {
      'artifact_bronze': 'Artefact Bronze',
      'artifact_silver': 'Artefact Argent', 
      'artifact_gold': 'Artefact Or',
      'manual_upgrade': 'Amélioration manuelle'
    }
  },
  
  // 图鉴类型
  artifacts: {
    bronze: 'Bronze',
    silver: 'Argent',
    gold: 'Or'
  },
  lottery: {
    lottery: 'Roulette',
    title: 'Roulette de la Fortune',
    start: 'Tourner',
    start_ing: 'Tourner',
    notice: 'Cliquez pour commencer à tourner et obtenir des récompenses aléatoires !',
    remaining_times: 'Temps restant:'
  },

  // 卡片视图系统
  cardView: {
    title: 'Vue Carte',
    guardian: 'Gardien',
    experience: 'Expérience',
    collection: 'Collection',
    achievements: 'Réalisations',
    upgrades: 'Améliorations'
  },

  // 守护者卡片
  guardian: {
    title: 'Gardien',
    subtitle: 'Informations du Gardien de la Ville',
    clickForDetails: 'Cliquer pour les détails',
    detailedInfo: 'Informations détaillées',
    artifactCollection: 'Collection d\'artefacts',
    artifacts: 'Artefacts',
    abilities: 'Capacités',
    actions: 'Actions',
    unlockedAbilities: 'Capacités débloquées',
    upgradeOptions: 'Options d\'amélioration',
    upgradeWithGold: 'Améliorer avec de l\'or',
    upgradeWithDiamond: 'Améliorer avec des diamants',
    upgradeSuccess: 'Amélioration réussie !',
    upgradeFailed: 'Amélioration échouée',
    rewardClaimed: 'Récompense réclamée',
    claimFailed: 'Réclamation échouée',
    rewardMultiplied: 'Récompense doublée',
    multiplyFailed: 'Doublement échoué',
    names: {
      novice: 'Gardien Novice',
      apprentice: 'Gardien Apprenti',
      experienced: 'Gardien Expérimenté',
      skilled: 'Gardien Compétent',
      veteran: 'Gardien Vétéran',
      expert: 'Gardien Expert',
      master: 'Gardien Maître',
      grandmaster: 'Gardien Grand Maître'
    },
    levels: {
      novice: 'Novice',
      apprentice: 'Apprenti',
      experienced: 'Expérimenté',
      skilled: 'Compétent',
      veteran: 'Vétéran',
      expert: 'Expert',
      master: 'Maître',
      grandmaster: 'Grand Maître'
    },
    descriptions: {
      level1: 'Un nouveau venu qui commence le chemin du gardien',
      level2: 'Un apprenti qui commence à maîtriser les compétences de base',
      level3: 'Un gardien avec une certaine expérience',
      level4: 'Un gardien compétent avec des techniques raffinées',
      level5: 'Un gardien vétéran expérimenté',
      level10: 'Un gardien expert avec des compétences magistrales',
      level12: 'Un gardien maître vénéré',
      level14: 'Un gardien grand maître légendaire'
    },
    abilities: {
      staminaBoost: 'Boost d\'endurance',
      staminaBoostDesc: 'Augmente l\'endurance maximale',
      treasureFinder: 'Chercheur de trésors',
      treasureFinderDesc: 'Augmente le taux de découverte de trésors',
      masterCollector: 'Maître Collectionneur',
      masterCollectorDesc: 'Double récompense pour toutes les actions de collection'
    }
  },

  // 经验系统
  experience: {
    title: 'Système d\'Expérience',
    subtitle: 'Progrès de croissance du Gardien',
    level: 'Niveau',
    progress: 'Progrès',
    expNeeded: '{exp} EXP nécessaires pour monter de niveau',
    sources: 'Sources d\'expérience',
    fromThieves: 'Des voleurs',
    fromGarbage: 'Des déchets',
    fromMonuments: 'Des monuments',
    fromBonus: 'Des bonus',
    nextLevel: 'Niveau suivant',
    maxLevelReached: 'Niveau maximum atteint',
    maxLevelDescription: 'Félicitations ! Vous êtes le gardien le plus fort !',
    viewHistory: 'Voir l\'historique',
    tips: 'Conseils'
  },

  // 收集统计
  collection: {
    statsTitle: 'Statistiques de Collection',
    statsSubtitle: 'Vos réalisations de collection',
    total: 'Total',
    todayCollected: 'Aujourd\'hui collecté {count}',
    bestDay: 'Meilleur jour {count}',
    avgPerDay: 'Moyenne quotidienne {count}',
    byCategory: 'Par catégorie',
    thieves: 'Voleurs',
    garbage: 'Déchets',
    monuments: 'Monuments',
    treasures: 'Trésors',
    achievements: 'Badges de réalisation',
    efficiency: 'Efficacité de collection',
    perMinute: 'par minute',
    accuracy: 'Précision',
    streak: 'Série',
    viewDetails: 'Voir les détails',
    share: 'Partager'
  },

  // 成就系统
  achievements: {
    firstThief: 'Première capture',
    firstThiefDesc: 'Capturez avec succès votre premier voleur',
    thiefHunter: 'Chasseur de voleurs',
    thiefHunterDesc: 'Capturez 100 voleurs au total',
    cleanCity: 'Nettoyeur de ville',
    cleanCityDesc: 'Nettoyez 50 déchets au total',
    guardianMaster: 'Maître Gardien',
    guardianMasterDesc: 'Terminez 1000 actions de collection',
    completed: 'Terminé',
    reward: 'Récompense',
    unlockedOn: 'Débloqué le'
  },

  // 升级系统
  upgrades: {
    staminaBoost: 'Amélioration d\'endurance',
    staminaBoostDesc: 'Augmente l\'endurance maximale de 20 points',
    treasureFinder: 'Expert en trésors',
    treasureFinderDesc: 'Augmente le taux de découverte de trésors de 25%',
    requirements: 'Exigences',
    requireLevel: 'Niveau {level} requis',
    requireThieves: 'Capture de {count} voleurs requise',
    requireCollections: '{count} collections requises',
    cost: 'Coût d\'amélioration',
    upgrade: 'Améliorer',
    locked: 'Verrouillé',
    upgradeSuccess: 'Amélioration réussie !',
    upgradeFailed: 'Amélioration échouée'
  },

  // 奖励系统
  rewards: {
    staminaBoost: 'Boost d\'endurance',
    treasureFinder: 'Chercheur de trésors',
    masterCollector: 'Maître Collectionneur',
    grandmasterTitle: 'Titre de Grand Maître',
    goldBonus: 'Bonus d\'or',
    diamondBonus: 'Bonus de diamants'
  },

  // Messages système
  system: {
    allPrizesClaimed: 'Tous les prix ont été réclamés, veuillez attendre 24 heures pour la réinitialisation',
    lotteryDataError: 'Erreur de données de loterie, veuillez réessayer',
    prizeDataError: 'Erreur de données de prix, veuillez réessayer',
    networkError: 'Erreur réseau, veuillez réessayer plus tard',
    loadingHistoryFailed: 'Échec du chargement de l\'historique',
    watchingAd: 'Regarder la publicité...',
    guardianUpgradeNote: 'Conformité PRD : Le niveau de gardien augmente automatiquement grâce aux actions de collection, aucun achat de devise n\'est nécessaire',
    allClaimed: 'Tout réclamé'
  },

  // Ressources de jeu
  resources: {
    civilizationExp: 'Expérience de civilisation',
    stamina: 'Endurance',
    staminaDepleted: 'Endurance épuisée !',
    staminaFull: 'L\'endurance est pleine, pas besoin de restaurer',
    staminaRestored: 'Restauré {amount} d\'endurance !',
    civilizationExpEarned: 'Gagné {amount} d\'expérience de civilisation !',
    repentanceBonus: 'Lettre de repentance obtenue ! {amount} d\'expérience de civilisation supplémentaire !'
  },

  // Carte Gardien
  guardianCard: {
    title: 'Gardien de la Ville',
    goldGuardian: 'Gardien d\'Or',
    description: 'Devenez gardien de la ville, à mesure que votre niveau augmente, vous gagnerez une certaine réputation de gardien par heure',
    reputationEarned: 'Réputation gagnée'
  },

  // Texte des boutons
  buttons: {
    get: 'Obtenir',
    double: 'Double'
  },

  // Déverrouillage de ville
  cityUnlock: {
    levelRequired: 'Niveau {level} requis',
    unlockMessage: 'Niveau {level} requis pour déverrouiller cette ville'
  },

  // Messages de récompense
  rewardMessages: {
    doubleReward: 'Récompense double',
    doubleRewardFailed: 'Échec de la réclamation de la récompense double',
    getDoubleRewardFailed: 'Échec de l\'obtention de la récompense double',
    watchAdDoubleReward: 'Regarder la pub pour une récompense double',
    doubleRewardButton: 'Bouton de récompense double'
  },

  // Messages de jeu
  gameMessages: {
    staminaRecovery: 'Récupération d\'endurance',
    staminaRecovered: 'Endurance récupérée à 100% !',
    guardianExpEarned: 'Gagné {amount} d\'expérience de gardien !',
    culturalAtlasEarned: 'Atlas culturel obtenu, converti en {amount} d\'expérience de gardien !',
    culturalAtlasCount: 'Obtenu {count} atlas culturels !',
    itemReward: 'Récompense d\'objet obtenue !',
    avatarEditPermission: 'Permission d\'édition de position d\'avatar obtenue ! Peut personnaliser la position de l\'avatar du gardien',
    watchAdExtra: 'Regarder la pub pour obtenir {item} supplémentaire !',
    levelResetNote: 'Réinitialiser le niveau ne supprimera que les enregistrements de collection de points chauds, les artefacts obtenus ne seront pas perdus',
    artifactsObtained: 'Artefacts obtenus ({count})',
    levelResetConfirm: 'Êtes-vous sûr de vouloir réinitialiser {levelType} ?\n\nAprès réinitialisation, vous pouvez re-collecter tous les points chauds de ce niveau, mais les artefacts obtenus ne seront pas perdus.',
    culturalHeritageRecord: 'Enregistrement du patrimoine culturel obtenu du coffre au trésor',
    staminaRecoveredWithCurrent: 'Endurance récupérée ! Endurance actuelle : {current}/{max}'
  },

  // Dialogue Boss
  bossDialogue: {
    getReward: 'Obtenir la récompense',
    watchAdForDouble: 'Regarder la pub pour doubler'
  },

  // Dialogue Boîte au Trésor
  treasureBoxDialog: {
    watchAdForDouble: 'Regarder la pub pour doubler'
  },

  // Quiz Monument
  monumentQuiz: {
    explanationWillBeObtained: 'Sera obtenu après soumission'
  },
} 