export default {
  common: {
    confirm: 'Confirmar',
    cancel: 'Cancelar',
    close: 'Cerrar',
    loading: 'Cargando...',
    tips: 'Consejos',
    warning: 'Advertencia',
    success: 'Éxito',
    error: 'Error',
    gold: 'Oro',
    diamond: '<PERSON><PERSON><PERSON>',
    hour: 'hora',
    hours: 'horas',
    minutes: 'minutos',
    details: 'Detalles',
    upgrade: 'Mejorar',
    purchase: 'Comprar',
    level: 'Nivel'
  },
  ranking: {
    title: 'Clasificación',
    rank: 'Rango',
    name: 'Nombre',
    gold: 'Oro',
    times: 'Veces',
  },
  home: {
    title: 'Búsqueda del tesoro en la ciudad',
    subtitle: 'Explora hermosos paisajes alrededor del mundo y encuentra tesoros ocultos',
    startGame: 'Comenzar juego',
    quickLogin: 'Inicio rápido',
    loginWith: {
      google: 'Iniciar con Google',
      facebook: 'Iniciar con Facebook',
      apple: 'Iniciar con Apple',
      wechat: 'Iniciar con WeChat'
    },
    loginDeveloping: 'El inicio de sesión con {platform} está en desarrollo...'
  },
  Tasks: 'Tareas',
  game: {
    attack: 'Atacar',
    level: 'Nv.{level}',
    coins: 'Monedas',
    time: 'Tiempo',
    map: 'Mapa',
    ranking: 'Clasificación',
    exp: 'Experiencia',
    lottery: {
      title: 'lotería',
    },
    tools: {
      radar: 'Radar',
      magnifier: 'Lupa',
      cooldown: '{seconds}s',
      watchAdForUse: 'Ver anuncio para usar',
      radarCooldownTip: 'El radar está recargando o sin usos',
      magnifierCooldownTip: 'La lupa está recargando o sin usos',
      radarScanned: '¡El radar ha detectado un objetivo!',
      magnifierUsed: '¡Los ladrones cercanos han sido ampliados!',
      noThievesNearby: 'No hay ladrones cerca',
      loadingAd: 'Cargando anuncio...',
      gotRadar: '+1 uso de radar',
      gotMagnifier: '+1 uso de lupa'
    },

    rewards: {
      caughtThief: '¡Ladrón atrapado!',
      cleanedGarbage: '¡Basura limpiada!',
      foundTreasure: '¡Tesoro encontrado!',
      watchAdForDouble: 'Ver anuncio para doble recompensa',
      gotDoubleReward: 'Recompensa doble +{amount}'
    },

    levelMap: {
      title: 'Mapa de niveles',
      locked: 'Este nivel está bloqueado',
      currentLevel: 'Ya en este nivel',
      switchToLevel: 'Cambiar al nivel {name}'
    },

    ranking: {
      title: 'Tabla de clasificación',
      rank: 'Rango',
      name: 'Nombre',
      gold: 'Oro',
      times: 'Veces',
      you: 'Tú'
    },
    attackFailed: 'Atacar fallado',
    attackSuccess: 'Atacar exitoso',
    notEnoughAmmo: 'Munición insuficiente'
  },

  cities: {
    rome: 'Roma',
    paris: 'París',
    newYork: 'Nueva York',
    shanghai: 'Shanghái',
    prague: 'Praga',
    zurich: 'Zúrich',
    iceland: 'Islandia',
    venice: 'Venecia'
  },

  settings: {
    title: 'Configuración del juego',
    language: 'Idioma',
    sound: 'Sonido',
    music: 'Música',
    vibration: 'Vibración',
    quality: 'Calidad',
    high: 'Alta',
    medium: 'Media',
    low: 'Baja'
  },

  guide: {
    skip: 'Saltar',
    prev: 'Anterior',
    next: 'Siguiente',
    start: '¡Comenzar aventura!',
    step1: {
      title: '¡Encuentra a los ladronzuelos traviesos!',
      description: '¡Usa tus dedos para acercar y arrastrar para encontrarlos!'
    },
    step2: {
      title: '¡El Rey de la Basura Espacial invade!',
      description: '¡Conviértete en guardián de la ciudad y salva el mundo!'
    },
    step3: {
      title: '¡Recolecta munición y dispara!',
      description: '¡Recolecta ladrones y basura para atacar al Rey!'
    },
    step4: {
      title: '¡Mejora el equipo y hazte más fuerte!',
      description: '¿Estás listo?'
    }
  },

  // 游戏菜单
  gameMenu: {
    title: 'Menú del Juego',
    cityGuide: 'Guía de la Ciudad',
    ranking: 'Clasificación',
    cardView: 'Vista de Tarjetas',
    collectionStats: 'Estadísticas de Colección',
    contactUs: 'Contáctanos',
    // 设置项
    music: 'Música',
    soundEffects: 'Efectos de Sonido',
    vibration: 'Vibración',
    language: 'Idioma',
    // 提示信息
    musicEnabled: 'Música activada',
    musicDisabled: 'Música desactivada',
    sfxEnabled: 'Efectos de sonido activados',
    sfxDisabled: 'Efectos de sonido desactivados',
    vibrationEnabled: 'Vibración activada',
    vibrationDisabled: 'Vibración desactivada',
    contactDev: 'Función de contacto en desarrollo...',
    showUI: 'Mostrar UI',
    hideUI: 'Ocultar UI'
  },
  Collections: 'Tarjetas',
  cityCollections: {
    title: 'Colecciones de ciudades',
    vatican: {
      title: 'Ciudades de Vaticano',
      card1: 'Carta de Vaticano 1',
      card2: 'Carta de Vaticano 2'
    },
    description: 'Cada colección cultural es un tesoro cultural de la ciudad, necesitas responder preguntas para obtener recompensas ricas'
  },
  
  // 被动收益系统
  passiveIncome: {
    title: 'Ingresos Pasivos',
    offlineTime: 'Tiempo sin conexión',
    hourlyRate: 'Tasa por hora',
    guardianBonus: 'Bono de Guardián',
    collect: 'Recolectar ingresos',
    collectWithAd: 'Ver anuncio para recolectar',
    noIncome: 'No hay ingresos para recolectar'
  },
  
  // 守护等级系统
  guardian: {
    title: 'Nivel de Guardián',
    level: 'Nivel',
    experience: 'Experiencia',
    artifactCollection: 'Colección de artefactos',
    totalArtifacts: 'Total de artefactos',
    unlockedAbilities: 'Habilidades desbloqueadas',
    manualUpgrade: 'Mejora manual',
    nextLevelCost: 'Costo del siguiente nivel',
    upgradeHistory: 'Historial de mejoras',
    loadHistory: 'Cargar historial',
    abilities: {
      'auto_collect': 'Recolección automática de ingresos pasivos',
      'extended_accumulation': 'Extender acumulación a 48 horas',
      'passive_income_boost': 'Impulso de ingresos pasivos',
      'ad_reward_boost': 'Doble recompensa de anuncios'
    },
    sources: {
      'artifact_bronze': 'Artefacto Bronce',
      'artifact_silver': 'Artefacto Plata', 
      'artifact_gold': 'Artefacto Oro',
      'manual_upgrade': 'Mejora manual'
    }
  },
  
  // 图鉴类型
  artifacts: {
    bronze: 'Bronce',
    silver: 'Plata',
    gold: 'Oro'
  },
  lottery: {
    lottery: 'Rueda',
    title: 'Rueda de la Fortuna',
    start: 'Girar',
    start_ing: 'Girando',
    notice: '¡Haz clic para girar y obtener recompensas aleatorias!',
    remaining_times: 'Tiempos restantes:'
  },

  // 卡片视图系统
  cardView: {
    title: 'Vista de Tarjetas',
    guardian: 'Guardián',
    experience: 'Experiencia',
    collection: 'Colección',
    achievements: 'Logros',
    upgrades: 'Mejoras'
  },

  // 守护者卡片
  guardian: {
    title: 'Guardián',
    subtitle: 'Información del Guardián de la Ciudad',
    clickForDetails: 'Haz clic para detalles',
    detailedInfo: 'Información detallada',
    artifactCollection: 'Colección de artefactos',
    artifacts: 'Artefactos',
    abilities: 'Habilidades',
    actions: 'Acciones',
    unlockedAbilities: 'Habilidades desbloqueadas',
    upgradeOptions: 'Opciones de mejora',
    upgradeWithGold: 'Mejorar con oro',
    upgradeWithDiamond: 'Mejorar con diamantes',
    upgradeSuccess: '¡Mejora exitosa!',
    upgradeFailed: 'Mejora fallida',
    rewardClaimed: 'Recompensa reclamada',
    claimFailed: 'Reclamación fallida',
    rewardMultiplied: 'Recompensa duplicada',
    multiplyFailed: 'Duplicación fallida',
    names: {
      novice: 'Guardián Novato',
      apprentice: 'Guardián Aprendiz',
      experienced: 'Guardián Experimentado',
      skilled: 'Guardián Hábil',
      veteran: 'Guardián Veterano',
      expert: 'Guardián Experto',
      master: 'Guardián Maestro',
      grandmaster: 'Guardián Gran Maestro'
    },
    levels: {
      novice: 'Novato',
      apprentice: 'Aprendiz',
      experienced: 'Experimentado',
      skilled: 'Hábil',
      veteran: 'Veterano',
      expert: 'Experto',
      master: 'Maestro',
      grandmaster: 'Gran Maestro'
    },
    descriptions: {
      level1: 'Un recién llegado que comienza el camino del guardián',
      level2: 'Un aprendiz que comienza a dominar habilidades básicas',
      level3: 'Un guardián con cierta experiencia',
      level4: 'Un guardián hábil con técnicas refinadas',
      level5: 'Un guardián veterano experimentado',
      level10: 'Un guardián experto con habilidades magistrales',
      level12: 'Un guardián maestro venerado',
      level14: 'Un guardián gran maestro legendario'
    },
    abilities: {
      staminaBoost: 'Impulso de resistencia',
      staminaBoostDesc: 'Aumenta la resistencia máxima',
      treasureFinder: 'Buscador de tesoros',
      treasureFinderDesc: 'Aumenta la tasa de descubrimiento de tesoros',
      masterCollector: 'Maestro Coleccionista',
      masterCollectorDesc: 'Doble recompensa de todas las acciones de colección'
    }
  },

  // 经验系统
  experience: {
    title: 'Sistema de Experiencia',
    subtitle: 'Progreso de crecimiento del Guardián',
    level: 'Nivel',
    progress: 'Progreso',
    expNeeded: 'Se necesitan {exp} EXP para subir de nivel',
    sources: 'Fuentes de experiencia',
    fromThieves: 'De ladrones',
    fromGarbage: 'De basura',
    fromMonuments: 'De monumentos',
    fromBonus: 'De bonos',
    nextLevel: 'Siguiente nivel',
    maxLevelReached: 'Nivel máximo alcanzado',
    maxLevelDescription: '¡Felicitaciones! ¡Eres el guardián más fuerte!',
    viewHistory: 'Ver historial',
    tips: 'Consejos'
  },

  // 收集统计
  collection: {
    statsTitle: 'Estadísticas de Colección',
    statsSubtitle: 'Tus logros de colección',
    total: 'Total',
    todayCollected: 'Hoy recolectado {count}',
    bestDay: 'Mejor día {count}',
    avgPerDay: 'Promedio diario {count}',
    byCategory: 'Por categoría',
    thieves: 'Ladrones',
    garbage: 'Basura',
    monuments: 'Monumentos',
    treasures: 'Tesoros',
    achievements: 'Insignias de logro',
    efficiency: 'Eficiencia de colección',
    perMinute: 'por minuto',
    accuracy: 'Precisión',
    streak: 'Racha',
    viewDetails: 'Ver detalles',
    share: 'Compartir'
  },

  // 成就系统
  achievements: {
    firstThief: 'Primera captura',
    firstThiefDesc: 'Captura exitosamente tu primer ladrón',
    thiefHunter: 'Cazador de ladrones',
    thiefHunterDesc: 'Captura 100 ladrones en total',
    cleanCity: 'Limpiador de ciudad',
    cleanCityDesc: 'Limpia 50 piezas de basura en total',
    guardianMaster: 'Maestro Guardián',
    guardianMasterDesc: 'Completa 1000 acciones de colección',
    completed: 'Completado',
    reward: 'Recompensa',
    unlockedOn: 'Desbloqueado el'
  },

  // 升级系统
  upgrades: {
    staminaBoost: 'Mejora de resistencia',
    staminaBoostDesc: 'Aumenta la resistencia máxima en 20 puntos',
    treasureFinder: 'Experto en tesoros',
    treasureFinderDesc: 'Aumenta la tasa de descubrimiento de tesoros en 25%',
    requirements: 'Requisitos',
    requireLevel: 'Requiere nivel {level}',
    requireThieves: 'Requiere capturar {count} ladrones',
    requireCollections: 'Requiere {count} colecciones',
    cost: 'Costo de mejora',
    upgrade: 'Mejorar',
    locked: 'Bloqueado',
    upgradeSuccess: '¡Mejora exitosa!',
    upgradeFailed: 'Mejora fallida'
  },

  // 奖励系统
  rewards: {
    staminaBoost: 'Impulso de resistencia',
    treasureFinder: 'Buscador de tesoros',
    masterCollector: 'Maestro Coleccionista',
    grandmasterTitle: 'Título de Gran Maestro',
    goldBonus: 'Bono de oro',
    diamondBonus: 'Bono de diamantes'
  },

  // Mensajes del sistema
  system: {
    allPrizesClaimed: 'Todos los premios han sido reclamados, por favor espera 24 horas para el reinicio',
    lotteryDataError: 'Error de datos de lotería, por favor intenta de nuevo',
    prizeDataError: 'Error de datos de premio, por favor intenta de nuevo',
    networkError: 'Error de red, por favor intenta de nuevo más tarde',
    loadingHistoryFailed: 'Error al cargar el historial',
    watchingAd: 'Viendo anuncio...',
    guardianUpgradeNote: 'Cumplimiento PRD: El nivel de guardián aumenta automáticamente a través de acciones de colección, no se necesita compra de moneda',
    allClaimed: 'Todo reclamado'
  },

  // Recursos del juego
  resources: {
    civilizationExp: 'Experiencia de Civilización',
    stamina: 'Resistencia',
    staminaDepleted: '¡Resistencia agotada!',
    staminaFull: 'La resistencia está llena, no es necesario restaurar',
    staminaRestored: '¡Restaurado {amount} de resistencia!',
    civilizationExpEarned: '¡Ganado {amount} de experiencia de civilización!',
    repentanceBonus: '¡Carta de arrepentimiento obtenida! ¡{amount} de experiencia de civilización extra!'
  },

  // Tarjeta de Guardián
  guardianCard: {
    title: 'Guardián de la Ciudad',
    goldGuardian: 'Guardián de Oro',
    description: 'Conviértete en guardián de la ciudad, a medida que tu nivel aumenta, ganarás cierta reputación de guardián por hora',
    reputationEarned: 'Reputación ganada'
  },

  // Texto de botones
  buttons: {
    get: 'Obtener',
    double: 'Doble'
  },

  // Desbloqueo de ciudad
  cityUnlock: {
    levelRequired: 'Nivel {level} requerido',
    unlockMessage: 'Nivel {level} requerido para desbloquear esta ciudad'
  },

  // Mensajes de recompensa
  rewardMessages: {
    doubleReward: 'Recompensa doble',
    doubleRewardFailed: 'Error al reclamar recompensa doble',
    getDoubleRewardFailed: 'Error al obtener recompensa doble',
    watchAdDoubleReward: 'Ver anuncio para recompensa doble',
    doubleRewardButton: 'Botón de recompensa doble'
  },

  // Mensajes del juego
  gameMessages: {
    staminaRecovery: 'Recuperación de resistencia',
    staminaRecovered: '¡Resistencia recuperada al 100%!',
    guardianExpEarned: '¡Ganado {amount} de experiencia de guardián!',
    culturalAtlasEarned: '¡Atlas cultural obtenido, convertido a {amount} de experiencia de guardián!',
    culturalAtlasCount: '¡Obtenidos {count} atlas culturales!',
    itemReward: '¡Recompensa de objeto obtenida!',
    avatarEditPermission: '¡Permiso de edición de posición de avatar obtenido! Puede personalizar la posición del avatar del guardián',
    watchAdExtra: '¡Ver anuncio para obtener {item} extra!',
    levelResetNote: 'Restablecer el nivel solo borrará los registros de colección de puntos calientes, los artefactos obtenidos no se perderán',
    artifactsObtained: 'Artefactos obtenidos ({count})',
    levelResetConfirm: '¿Estás seguro de que quieres restablecer {levelType}?\n\nDespués del restablecimiento, puedes re-coleccionar todos los puntos calientes de este nivel, pero los artefactos obtenidos no se perderán.',
    culturalHeritageRecord: 'Registro de patrimonio cultural obtenido del cofre del tesoro',
    staminaRecoveredWithCurrent: '¡Resistencia recuperada! Resistencia actual: {current}/{max}'
  },

  // Diálogo de Boss
  bossDialogue: {
    getReward: 'Obtener recompensa',
    watchAdForDouble: 'Ver anuncio para doble'
  },

  // Diálogo de Caja del Tesoro
  treasureBoxDialog: {
    watchAdForDouble: 'Ver anuncio para doble'
  },

  // Cuestionario de Monumento
  monumentQuiz: {
    explanationWillBeObtained: 'Will be obtained after submission'
  },
} 