export default {
  common: {
    confirm: '確認',
    cancel: 'キャンセル',
    close: '閉じる',
    loading: '読み込み中...',
    tips: 'ヒント',
    warning: '警告',
    success: '成功',
    error: 'エラー',
    gold: 'ゴールド',
    diamond: 'ダイヤモンド',
    hour: '時間',
    hours: '時間',
    minutes: '分',
    details: '詳細',
    upgrade: 'アップグレード',
    purchase: '購入',
    level: 'レベル'
  },
  ranking: {
    title: 'ランキング',
    rank: 'ランク',
    name: '名前',
    gold: 'ゴールド',
    times: '回数',
  },
  home: {
    title: 'シティトレジャーハント',
    subtitle: '世界中の美しい景色を探索し、隠された宝物を見つけよう',
    startGame: 'ゲーム開始',
    quickLogin: 'クイックログイン',
    loginWith: {
      google: 'Googleでログイン',
      facebook: 'Facebookでログイン',
      apple: 'Appleでログイン',
      wechat: 'WeChatでログイン'
    },
    loginDeveloping: '{platform}ログイン機能は開発中です...'
  },
  Tasks: 'タスク',
  game: {
    attack: '攻撃',
    level: 'Lv.{level}',
    coins: 'コイン',
    time: '時間',
    map: 'マップ',
    ranking: 'ランキング',
    exp: '経験値',
    lottery: {
      title: 'スピン',
    },
    tools: {
      radar: 'レーダー',
      magnifier: '虫眼鏡',
      cooldown: '{seconds}秒',
      watchAdForUse: '広告を見て使用',
      radarCooldownTip: 'レーダーはクールダウン中または使用回数不足',
      magnifierCooldownTip: '虫眼鏡はクールダウン中または使用回数不足',
      radarScanned: 'レーダーが目標を検出しました！',
      magnifierUsed: '近くの泥棒が拡大されました！',
      noThievesNearby: '近くに泥棒がいません',
      loadingAd: '広告を読み込み中...',
      gotRadar: 'レーダー使用回数+1',
      gotMagnifier: '虫眼鏡使用回数+1'
    },
    
    rewards: {
      caughtThief: '泥棒を捕まえた！',
      cleanedGarbage: 'ゴミを片付けた！',
      foundTreasure: '宝箱を発見！',
      watchAdForDouble: '広告を見て報酬2倍',
      gotDoubleReward: '2倍の報酬を獲得 +{amount}'
    },
    
    levelMap: {
      title: 'レベルマップ',
      locked: 'このレベルはロックされています',
      currentLevel: '現在このレベルにいます',
      switchToLevel: '{name}レベルに切り替え'
    },
    
    ranking: {
      title: 'リーダーボード',
      rank: 'ランク',
      name: '名前',
      gold: 'ゴールド',
      times: '回数',
      you: 'あなた'
    },
    attackFailed: '攻撃失敗',
    attackSuccess: '攻撃成功',
    notEnoughAmmo: '弾薬不足'
  },
  
  cities: {
    rome: 'ローマ',
    paris: 'パリ',
    newYork: 'ニューヨーク',
    shanghai: '上海',
    prague: 'プラハ',
    zurich: 'チューリッヒ',
    iceland: 'アイスランド',
    venice: 'ヴェネツィア'
  },
  
  settings: {
    title: 'ゲーム設定',
    language: '言語設定',
    sound: 'サウンド',
    music: '音楽',
    vibration: '振動',
    quality: '画質',
    high: '高',
    medium: '中',
    low: '低'
  },
  
  guide: {
    skip: 'スキップ',
    prev: '前へ',
    next: '次へ',
    start: '冒険を始める！',
    step1: {
      title: 'いたずらな泥棒を見つけよう！',
      description: '指でズームやドラッグして泥棒を見つけよう！'
    },
    step2: {
      title: '宇宙ゴミ大王が侵入！',
      description: '街の守護者になって世界を救おう！'
    },
    step3: {
      title: '弾薬を集めて、発射！',
      description: '泥棒とゴミを集めて、大王を攻撃しよう！'
    },
    step4: {
      title: '装備をアップグレード、強くなろう！',
      description: '準備はいいですか？'
    }
  },
  
  // 游戏菜单
  gameMenu: {
    title: 'ゲームメニュー',
    cityGuide: 'シティガイド',
    ranking: 'ランキング',
    cardView: 'カードビュー',
    collectionStats: '収集統計',
    contactUs: 'お問い合わせ',
    // 设置项
    music: '音楽',
    soundEffects: '効果音',
    vibration: '振動',
    language: '言語',
    // 提示信息
    musicEnabled: '音楽がオンになりました',
    musicDisabled: '音楽がオフになりました',
    sfxEnabled: '効果音がオンになりました',
    sfxDisabled: '効果音がオフになりました',
    vibrationEnabled: '振動がオンになりました',
    vibrationDisabled: '振動がオフになりました',
    contactDev: 'お問い合わせ機能は開発中です...',
    showUI: 'UIを表示',
    hideUI: 'UIを非表示'
  },
  Collections: '図鑑',
  cityCollections: {
    title: '都市コレクション',
    vatican: {
      title: 'バチカン市',
      card1: 'バチカンカード1',
      card2: 'バチカンカード2'
    },
    description: '各文化コレクションは都市の文化財であり、回答することで豊富な報酬を得ることができます'
  },
  
  // 被动收益系统
  passiveIncome: {
    title: 'パッシブ収入',
    offlineTime: 'オフライン時間',
    hourlyRate: '時間あたりの収入',
    guardianBonus: 'ガーディアンボーナス',
    collect: '収入を回収',
    collectWithAd: '広告を見て回収',
    noIncome: '回収可能な収入がありません'
  },
  
  // 守护等级系统
  guardian: {
    title: 'ガーディアンレベル',
    level: 'レベル',
    experience: '経験値',
    artifactCollection: '図鑑収集',
    totalArtifacts: '総図鑑数',
    unlockedAbilities: 'アンロック済み能力',
    manualUpgrade: '手動アップグレード',
    nextLevelCost: '次のレベル費用',
    upgradeHistory: 'アップグレード履歴',
    loadHistory: '履歴を読み込み',
    abilities: {
      'auto_collect': 'パッシブ収入の自動回収',
      'extended_accumulation': '蓄積時間を48時間に延長',
      'passive_income_boost': 'パッシブ収入ブースト',
      'ad_reward_boost': '広告報酬2倍'
    },
    sources: {
      'artifact_bronze': 'ブロンズ図鑑',
      'artifact_silver': 'シルバー図鑑', 
      'artifact_gold': 'ゴールド図鑑',
      'manual_upgrade': '手動アップグレード'
    }
  },
  
  // 图鉴类型
  artifacts: {
    bronze: 'ブロンズ',
    silver: 'シルバー',
    gold: 'ゴールド'
  },
  lottery: {
    lottery: 'スピン',
    title: 'ラッキースピン',
    start: 'スピン',
    start_ing: 'スピン中',
    notice: 'クリックしてスピンを開始し、ランダムな報酬を獲得しましょう！',
    remaining_times: '残り回数:'
  },

  // 卡片视图系统
  cardView: {
    title: 'カードビュー',
    guardian: 'ガーディアン',
    experience: '経験値',
    collection: '収集',
    achievements: '実績',
    upgrades: 'アップグレード'
  },

  // 守护者卡片
  guardian: {
    title: 'ガーディアン',
    subtitle: '都市ガーディアン情報',
    clickForDetails: '詳細をクリック',
    detailedInfo: '詳細情報',
    artifactCollection: '図鑑収集',
    artifacts: '図鑑',
    abilities: '能力',
    actions: 'アクション',
    unlockedAbilities: 'アンロック済み能力',
    upgradeOptions: 'アップグレードオプション',
    upgradeWithGold: 'ゴールドでアップグレード',
    upgradeWithDiamond: 'ダイヤモンドでアップグレード',
    upgradeSuccess: 'アップグレード成功！',
    upgradeFailed: 'アップグレード失敗',
    rewardClaimed: '報酬を獲得しました',
    claimFailed: '獲得失敗',
    rewardMultiplied: '報酬が2倍になりました',
    multiplyFailed: '2倍化失敗',
    names: {
      novice: '初心者ガーディアン',
      apprentice: '見習いガーディアン',
      experienced: '経験豊富なガーディアン',
      skilled: '熟練ガーディアン',
      veteran: 'ベテランガーディアン',
      expert: 'エキスパートガーディアン',
      master: 'マスターガーディアン',
      grandmaster: 'グランドマスターガーディアン'
    },
    levels: {
      novice: '初心者',
      apprentice: '見習い',
      experienced: '経験豊富',
      skilled: '熟練',
      veteran: 'ベテラン',
      expert: 'エキスパート',
      master: 'マスター',
      grandmaster: 'グランドマスター'
    },
    descriptions: {
      level1: 'ガーディアンの道を始めたばかりの新人',
      level2: '基本スキルを習得し始めた見習い',
      level3: '一定の経験を積んだガーディアン',
      level4: 'スキルが熟練したガーディアン',
      level5: '経験豊富なベテランガーディアン',
      level10: '技術に長けたエキスパート級ガーディアン',
      level12: '尊敬されるマスター級ガーディアン',
      level14: '伝説のグランドマスター級ガーディアン'
    },
    abilities: {
      staminaBoost: 'スタミナブースト',
      staminaBoostDesc: '最大スタミナ値を増加',
      treasureFinder: '宝探しの専門家',
      treasureFinderDesc: '宝箱発見率を向上',
      masterCollector: '収集マスター',
      masterCollectorDesc: 'すべての収集行為で2倍の報酬を獲得'
    }
  },

  // 经验系统
  experience: {
    title: '経験値システム',
    subtitle: 'ガーディアン成長進捗',
    level: 'レベル',
    progress: '進捗',
    expNeeded: 'レベルアップまで {exp} 経験値必要',
    sources: '経験値の源',
    fromThieves: '泥棒から',
    fromGarbage: 'ゴミから',
    fromMonuments: '記念碑から',
    fromBonus: 'ボーナスから',
    nextLevel: '次のレベル',
    maxLevelReached: '最高レベル到達',
    maxLevelDescription: 'おめでとう！あなたは最強のガーディアンです！',
    viewHistory: '履歴を表示',
    tips: '取得のコツ'
  },

  // 收集统计
  collection: {
    statsTitle: '収集統計',
    statsSubtitle: 'あなたの収集成績',
    total: '合計',
    todayCollected: '今日収集 {count}',
    bestDay: '最高単日 {count}',
    avgPerDay: '日平均 {count}',
    byCategory: 'カテゴリ別統計',
    thieves: '泥棒',
    garbage: 'ゴミ',
    monuments: '記念碑',
    treasures: '宝箱',
    achievements: '実績バッジ',
    efficiency: '収集効率',
    perMinute: '分あたり',
    accuracy: '正確率',
    streak: '連続',
    viewDetails: '詳細を表示',
    share: '共有'
  },

  // 成就系统
  achievements: {
    firstThief: '初回捕獲',
    firstThiefDesc: '最初の泥棒を成功裏に捕獲',
    thiefHunter: '泥棒ハンター',
    thiefHunterDesc: '累計100人の泥棒を捕獲',
    cleanCity: '都市清掃員',
    cleanCityDesc: '累計50個のゴミを清掃',
    guardianMaster: 'ガーディアンマスター',
    guardianMasterDesc: '1000回の収集行為を完了',
    completed: '完了',
    reward: '報酬',
    unlockedOn: 'アンロック日'
  },

  // 升级系统
  upgrades: {
    staminaBoost: 'スタミナ強化',
    staminaBoostDesc: '最大スタミナ値を20ポイント増加',
    treasureFinder: '宝探しの専門家',
    treasureFinderDesc: '宝箱発見率を25%向上',
    requirements: '必要条件',
    requireLevel: 'レベル {level} が必要',
    requireThieves: '{count} 人の泥棒を捕獲する必要があります',
    requireCollections: '{count} 回の収集を完了する必要があります',
    cost: 'アップグレード費用',
    upgrade: 'アップグレード',
    locked: 'アンロックされていません',
    upgradeSuccess: 'アップグレード成功！',
    upgradeFailed: 'アップグレード失敗'
  },

  // 奖励系统
  rewards: {
    staminaBoost: 'スタミナブースト',
    treasureFinder: '宝探しブースト',
    masterCollector: '収集マスター',
    grandmasterTitle: 'グランドマスター称号',
    goldBonus: 'ゴールド報酬',
    diamondBonus: 'ダイヤモンド報酬'
  },

  // システムメッセージ
  system: {
    allPrizesClaimed: 'すべての賞品が獲得済みです。24時間後にリセットされます',
    lotteryDataError: '抽選データエラー、再試行してください',
    prizeDataError: '賞品データエラー、再試行してください',
    networkError: 'ネットワークエラー、後でもう一度お試しください',
    loadingHistoryFailed: '履歴の読み込みに失敗しました',
    watchingAd: '広告を視聴中...',
    guardianUpgradeNote: 'PRD準拠：ガーディアンレベルは収集行為により自動的に上昇し、通貨購入は不要です',
    allClaimed: '獲得済み'
  },

  // ゲームリソース
  resources: {
    civilizationExp: '文明経験値',
    stamina: 'スタミナ',
    staminaDepleted: 'スタミナが不足しています！',
    staminaFull: 'スタミナが満タンです、回復の必要はありません',
    staminaRestored: '{amount} スタミナを回復しました！',
    civilizationExpEarned: '{amount} 文明経験値を獲得しました！',
    repentanceBonus: '悔過書を獲得！追加で {amount} 文明経験値を獲得しました！'
  },

  // ガーディアンカード
  guardianCard: {
    title: '都市ガーディアン',
    goldGuardian: 'ゴールドガーディアン',
    description: '都市ガーディアンになり、レベルが上がるにつれて、時間あたり一定のガーディアン声望を獲得します',
    reputationEarned: '獲得済み声望'
  },

  // ボタンテキスト
  buttons: {
    get: '取得',
    double: '2倍'
  },

  // 都市アンロック
  cityUnlock: {
    levelRequired: 'レベル {level} が必要です',
    unlockMessage: 'この都市をアンロックするにはレベル {level} が必要です'
  },

  // 報酬メッセージ
  rewardMessages: {
    doubleReward: '2倍報酬',
    doubleRewardFailed: '2倍報酬の獲得に失敗しました',
    getDoubleRewardFailed: '2倍報酬の取得に失敗しました',
    watchAdDoubleReward: '広告を見て2倍報酬',
    doubleRewardButton: '2倍報酬ボタン'
  },

  // ゲームメッセージ
  gameMessages: {
    staminaRecovery: 'スタミナ回復',
    staminaRecovered: 'スタミナが100%回復しました！',
    guardianExpEarned: '{amount} ガーディアン経験値を獲得しました！',
    culturalAtlasEarned: '文化図鑑を獲得、{amount} ガーディアン経験値に変換されました！',
    culturalAtlasCount: '{count} 個の文化図鑑を獲得しました！',
    itemReward: 'アイテム報酬を獲得しました！',
    avatarEditPermission: 'アバター位置編集権限を獲得しました！ガーディアンアバター位置をカスタマイズできます',
    watchAdExtra: '広告を見て追加で {item} を獲得！',
    levelResetNote: 'レベルリセットはホットスポット収集記録のみをクリアし、獲得済み図鑑は失われません',
    artifactsObtained: '獲得済み図鑑 ({count})',
    levelResetConfirm: '{levelType} をリセットしますか？\n\nリセット後、このレベルのすべてのホットスポットを再収集できますが、獲得済み図鑑は失われません。',
    culturalHeritageRecord: '宝箱から文化遺産記録を獲得',
    staminaRecoveredWithCurrent: 'スタミナが回復しました！現在のスタミナ: {current}/{max}'
  },

  // Boss対話
  bossDialogue: {
    getReward: '報酬を獲得',
    watchAdForDouble: '広告を見て2倍'
  },

  // 宝箱ダイアログ
  treasureBoxDialog: {
    watchAdForDouble: '広告を見て2倍'
  },

  // 記念碑クイズ
  monumentQuiz: {
    explanationWillBeObtained: '提出後に獲得されます'
  },
} 