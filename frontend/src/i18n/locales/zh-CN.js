export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    close: '关闭',
    loading: '加载中...',
    tips: '提示',
    warning: '警告',
    success: '成功',
    error: '错误',
    gold: '金币',
    diamond: '钻石',
    hour: '小时',
    hours: '小时',
    minutes: '分钟',
    details: '详情',
    upgrade: '升级',
    purchase: '购买',
    level: '等级'
  },
  ranking: {
    title: '排行榜',
    rank: '排名',
    name: '名称',
    gold: '金币',
    times: '次数',
  },
  home: {
    title: '城市探宝',
    subtitle: '探索世界各地的美景，寻找隐藏的宝藏',
    startGame: '开始游戏',
    quickLogin: '快速登录',
    loginWith: {
      google: 'Google登录',
      facebook: 'Facebook登录', 
      apple: 'Apple登录',
      wechat: '微信登录'
    },
    loginDeveloping: '{platform}登录功能开发中...'
  },
  Tasks: '任务',
  game: {
    attack: '攻击',
    level: 'Lv.{level}',
    coins: '金币',
    time: '时间',
    map: '地图',
    ranking: '排行榜',
    exp: '经验',
    lottery: {
      title: '摇奖',
    },
    tools: {
      radar: '雷达',
      magnifier: '放大镜',
      cooldown: '{seconds}秒',
      watchAdForUse: '看广告使用',
      radarCooldownTip: '雷达冷却中或次数不足',
      magnifierCooldownTip: '放大镜冷却中或次数不足',
      radarScanned: '雷达已扫描到目标！',
      magnifierUsed: '附近的小偷已被放大！',
      noThievesNearby: '附近没有小偷了',
      loadingAd: '正在加载广告...',
      gotRadar: '获得雷达次数+1',
      gotMagnifier: '获得放大镜次数+1'
    },
    
    rewards: {
      caughtThief: '抓到小偷！',
      cleanedGarbage: '清理垃圾！',
      foundTreasure: '发现宝箱！',
      watchAdForDouble: '看广告获得双倍奖励',
      gotDoubleReward: '获得双倍奖励 +{amount}'
    },
    
    levelMap: {
      title: '关卡地图',
      locked: '该关卡尚未解锁',
      currentLevel: '当前已在该关卡',
      switchToLevel: '切换到{name}关卡'
    },
    
    ranking: {
      title: '排行榜',
      rank: '排名',
      name: '名称',
      gold: '金币',
      times: '次数',
      you: '你'
    },
    attackFailed: '攻击失败',
    attackSuccess: '攻击成功',
    notEnoughAmmo: '弹药不足'
  },
  
  cities: {
    rome: '罗马',
    paris: '巴黎',
    newYork: '纽约',
    shanghai: '上海',
    prague: '布拉格',
    zurich: '苏黎世',
    iceland: '冰岛',
    venice: '威尼斯'
  },
  
  settings: {
    title: '游戏设置',
    language: '语言设置',
    sound: '音效',
    music: '音乐',
    vibration: '震动',
    quality: '画质',
    high: '高',
    medium: '中',
    low: '低'
  },
  
  guide: {
    skip: '跳过',
    prev: '上一步',
    next: '下一步',
    start: '开始冒险！',
    step1: {
      title: '寻找淘气的小偷！',
      description: '用你的手指放大和拖动来寻找他们吧！'
    },
    step2: {
      title: '宇宙垃圾大王入侵！',
      description: '快成为城市守护者，拯救世界！'
    },
    step3: {
      title: '收集弹药，开火！',
      description: '收集小偷和垃圾空团，攻击大王！'
    },
    step4: {
      title: '升级装备，变强！',
      description: '准备好了吗？'
    }
  },
  
  // 游戏菜单
  gameMenu: {
    title: '游戏菜单',
    cityGuide: '城市图鉴',
    ranking: '排行榜',
    cardView: '卡片视图',
    collectionStats: '收集统计',
    contactUs: '联系我们',
    // 设置项
    music: '音乐',
    soundEffects: '音效',
    vibration: '震动',
    language: '语言',
    // 提示信息
    musicEnabled: '音乐已开启',
    musicDisabled: '音乐已关闭',
    sfxEnabled: '音效已开启',
    sfxDisabled: '音效已关闭',
    vibrationEnabled: '震动已开启',
    vibrationDisabled: '震动已关闭',
    contactDev: '联系我们功能开发中...',
    showUI: '显示UI',
    hideUI: '隐藏UI'
  },
  Collections: '图鉴',
  cityCollections: {
    title: '城市收藏',
    vatican: {
      title: '梵蒂冈城市',
      card1: '梵蒂冈卡1',
      card2: '梵蒂冈卡2'
    },
    description: '每个文化图鉴都是这个城市的文化瑰宝，您需要回答问题才能获得丰厚的奖励'
  },
  
  // 被动收益系统
  passiveIncome: {
    title: '被动收益',
    offlineTime: '离线时长',
    hourlyRate: '每小时收益',
    guardianBonus: '守护加成',
    collect: '领取收益',
    collectWithAd: '观看广告领取',
    noIncome: '暂无可领取收益'
  },
  
  // 守护等级系统
  guardian: {
    title: '守护等级',
    level: '等级',
    experience: '经验',
    artifactCollection: '图鉴收集',
    totalArtifacts: '总图鉴数',
    unlockedAbilities: '已解锁能力',
    manualUpgrade: '手动升级',
    nextLevelCost: '下一级花费',
    upgradeHistory: '升级历史',
    loadHistory: '加载历史',
    abilities: {
      'auto_collect': '自动领取被动收益',
      'extended_accumulation': '延长积累时间至48小时',
      'passive_income_boost': '被动收益加成',
      'ad_reward_boost': '广告奖励翻倍'
    },
    sources: {
      'artifact_bronze': '青铜图鉴',
      'artifact_silver': '白银图鉴', 
      'artifact_gold': '黄金图鉴',
      'manual_upgrade': '手动升级'
    }
  },
  
  // 图鉴类型
  artifacts: {
    bronze: '青铜',
    silver: '白银',
    gold: '黄金'
  },
  lottery: {
    lottery: '抽奖',
    title: '幸运转盘',
    start: '摇奖',
    start_ing: '摇奖中',
    notice: '点击开始抽奖，即可获得随机奖励！',
    remaining_times: '剩余次数:'
  },

  // 卡片视图系统
  cardView: {
    title: '卡片视图',
    guardian: '守护者',
    experience: '经验',
    collection: '收集',
    achievements: '成就',
    upgrades: '升级'
  },

  // 守护者卡片
  guardian: {
    title: '守护者',
    subtitle: '城市守护者信息',
    clickForDetails: '点击查看详情',
    detailedInfo: '详细信息',
    artifactCollection: '图鉴收集',
    artifacts: '图鉴',
    abilities: '能力',
    actions: '行动',
    unlockedAbilities: '已解锁能力',
    upgradeOptions: '升级选项',
    upgradeWithGold: '金币升级',
    upgradeWithDiamond: '钻石升级',
    upgradeSuccess: '升级成功！',
    upgradeFailed: '升级失败',
    names: {
      novice: '新手守护者',
      apprentice: '学徒守护者',
      experienced: '经验守护者',
      skilled: '熟练守护者',
      veteran: '资深守护者',
      expert: '专家守护者',
      master: '大师守护者',
      grandmaster: '宗师守护者'
    },
    levels: {
      novice: '新手',
      apprentice: '学徒',
      experienced: '经验丰富',
      skilled: '熟练',
      veteran: '资深',
      expert: '专家',
      master: '大师',
      grandmaster: '宗师'
    },
    descriptions: {
      level1: '刚刚踏上守护者之路的新人',
      level2: '开始掌握基本技能的学徒',
      level3: '积累了一定经验的守护者',
      level4: '技能娴熟的守护者',
      level5: '经验丰富的资深守护者',
      level10: '技艺精湛的专家级守护者',
      level12: '德高望重的大师级守护者',
      level14: '传说中的宗师级守护者'
    },
    abilities: {
      staminaBoost: '体力加成',
      staminaBoostDesc: '增加最大体力值',
      treasureFinder: '寻宝专家',
      treasureFinderDesc: '提高宝箱发现率',
      masterCollector: '收集大师',
      masterCollectorDesc: '所有收集行为获得双倍奖励'
    }
  },

  // 经验系统
  experience: {
    title: '经验系统',
    subtitle: '守护者成长进度',
    level: '等级',
    progress: '进度',
    expNeeded: '还需 {exp} 经验升级',
    sources: '经验来源',
    fromThieves: '来自小偷',
    fromGarbage: '来自垃圾',
    fromMonuments: '来自古迹',
    fromBonus: '来自奖励',
    nextLevel: '下一等级',
    maxLevelReached: '已达最高等级',
    maxLevelDescription: '恭喜！您已经是最强的守护者了！',
    viewHistory: '查看历史',
    tips: '获取技巧'
  },

  // 收集统计
  collection: {
    statsTitle: '收集统计',
    statsSubtitle: '您的收集成就',
    total: '总计',
    todayCollected: '今日收集 {count}',
    bestDay: '最佳单日 {count}',
    avgPerDay: '日均 {count}',
    byCategory: '分类统计',
    thieves: '小偷',
    garbage: '垃圾',
    monuments: '古迹',
    treasures: '宝箱',
    achievements: '成就徽章',
    efficiency: '收集效率',
    perMinute: '每分钟',
    accuracy: '准确率',
    streak: '连击',
    viewDetails: '查看详情',
    share: '分享'
  },

  // 成就系统
  achievements: {
    firstThief: '初次抓捕',
    firstThiefDesc: '成功抓捕第一个小偷',
    thiefHunter: '小偷猎人',
    thiefHunterDesc: '累计抓捕100个小偷',
    cleanCity: '城市清洁工',
    cleanCityDesc: '累计清理50个垃圾',
    guardianMaster: '守护大师',
    guardianMasterDesc: '完成1000次收集行为',
    completed: '已完成',
    reward: '奖励',
    unlockedOn: '解锁于'
  },

  // 升级系统
  upgrades: {
    staminaBoost: '体力增强',
    staminaBoostDesc: '增加20点最大体力值',
    treasureFinder: '寻宝专家',
    treasureFinderDesc: '提高25%宝箱发现率',
    requirements: '需求条件',
    requireLevel: '需要等级 {level}',
    requireThieves: '需要抓捕 {count} 个小偷',
    requireCollections: '需要完成 {count} 次收集',
    cost: '升级费用',
    upgrade: '升级',
    locked: '未解锁',
    upgradeSuccess: '升级成功！',
    upgradeFailed: '升级失败',
    rewardClaimed: '奖励已领取',
    claimFailed: '领取失败',
    rewardMultiplied: '奖励已翻倍',
    multiplyFailed: '翻倍失败'
  },

  // 奖励系统
  rewards: {
    staminaBoost: '体力加成',
    treasureFinder: '寻宝加成',
    masterCollector: '收集大师',
    grandmasterTitle: '宗师称号',
    goldBonus: '金币奖励',
    diamondBonus: '钻石奖励'
  },

  // 系统消息
  system: {
    allPrizesClaimed: '所有奖品已抽完，请等待24小时重置',
    lotteryDataError: '抽奖数据异常，请重试',
    prizeDataError: '奖品数据异常，请重试',
    networkError: '网络错误，请稍后重试',
    loadingHistoryFailed: '加载历史失败',
    watchingAd: '观看广告中...',
    guardianUpgradeNote: 'PRD合规性：守护者等级通过收集行为自动提升，无需货币购买',
    allClaimed: '已抽完'
  },

  // 游戏资源
  resources: {
    civilizationExp: '文明经验值',
    stamina: '体力值',
    staminaDepleted: '体力值耗尽!',
    staminaFull: '体力值已满，无需恢复',
    staminaRestored: '恢复 {amount} 体力值！',
    civilizationExpEarned: '获得 {amount} 文明经验值！',
    repentanceBonus: '获得悔过书！额外获得 {amount} 文明经验值！'
  },

  // 守护者卡片
  guardianCard: {
    title: '城市守卫',
    goldGuardian: '黄金守卫',
    description: '成为城市守护者，随着等级的增加，每小时将获得一定守护声望',
    reputationEarned: '已获得声望'
  },

  // 按钮文本
  buttons: {
    get: '获取',
    double: '双倍'
  },

  // 城市解锁
  cityUnlock: {
    levelRequired: '需要等级 {level}',
    unlockMessage: '需要等级 {level} 才能解锁此城市'
  },

  // 奖励消息
  rewardMessages: {
    doubleReward: '双倍奖励',
    doubleRewardFailed: '领取双倍奖励失败',
    getDoubleRewardFailed: '获取双倍奖励失败',
    watchAdDoubleReward: '看广告双倍奖励',
    doubleRewardButton: '双倍奖励按钮'
  },

  // 游戏消息
  gameMessages: {
    staminaRecovery: '体力恢复',
    staminaRecovered: '体力已恢复100%!',
    guardianExpEarned: '获得 {amount} 守护者经验值！',
    culturalAtlasEarned: '获得文化图鉴，转换为 {amount} 守护者经验值！',
    culturalAtlasCount: '获得 {count} 个文化图鉴！',
    itemReward: '获得道具奖励！',
    avatarEditPermission: '获得头像位置编辑权限！可以自定义守望者头像位置',
    watchAdExtra: '观看广告获得额外 {item}！',
    levelResetNote: '重置关卡只会清除热点收集记录，已获得的图鉴不会丢失',
    artifactsObtained: '已获得图鉴 ({count})',
    levelResetConfirm: '确定要重置{levelType}吗？\n\n重置后可以重新收集该关卡的所有热点，但已获得的图鉴不会丢失。',
    culturalHeritageRecord: '从宝箱中获得的文化遗产记录',
    staminaRecoveredWithCurrent: '体力恢复！当前体力: {current}/{max}'
  },

  // Boss对话
  bossDialogue: {
    getReward: '获得奖励',
    watchAdForDouble: '看广告获得双倍'
  },

  // 宝箱对话框
  treasureBoxDialog: {
    watchAdForDouble: '观看广告 获得双倍'
  },

  // 纪念碑问答
  monumentQuiz: {
    explanationWillBeObtained: '将在提交后获得'
  },
} 