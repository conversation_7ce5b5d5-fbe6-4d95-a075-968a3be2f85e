<template>
  <van-popup
    v-model:show="visible"
    position="center"
    closeable
    class="city-collection-popup"
    :close-icon="'/img/page_icons/close-btn.png'"
    close-icon-position="top-right"
    round
  >
    <div class="city-collection-container">
      <div class="collection-header">
        {{ t('cityCollections.title') }}
      </div>

      <div class="city-selector">
        <div class="city-collections">
          <div class='description'>
            {{t('cityCollections.description')}}
          </div>
          <!-- 收藏品网格 -->
          <div class="collections-grid">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state">
              <van-loading size="24px" color="#FFD700">加载中...</van-loading>
            </div>

            <!-- 收藏品列表 -->
            <div
              v-for="item in currentCityItems"
              :key="item.item_id"
              class="collection-item"
              :class="{
                rare: item.rarity === 'rare',
                epic: item.rarity === 'epic',
                collected: item.owned,
                locked: !item.owned,
                flipping: item.isFlipping,
                'quiz-completed': item.hasCompletedQuiz
              }"
              @click="showItemDetail(item)"
            >
              <div class="card-container" :class="{ 'flip-animation': item.isFlipping }">
                <div class="card-front">
                  <div class="item-frame" :class="getRarityClass(item.rarity)">
                    <img
                      v-if="item.owned && item.image_url"
                      src="/img/page_icons/tujian_icon.png"
                      :alt="item.name"
                      class="item-image"
                    />
                    <div v-else class="item-locked">
                      <van-icon name="question-o" size="24" />
                    </div>
                    
                    <!-- 问答完成状态指示器 -->
                    <div v-if="item.hasCompletedQuiz" class="quiz-completed-badge">
                      <van-icon name="success" size="16" color="#fff" />
                    </div>
                  </div>
                  <div class="item-info">
                    {{ item.owned ? item.name : 'The Last Judgment' }}
                  </div>
                </div>
                <div class="card-back">
                  <div class="card-back-content">
                    <van-icon name="question-o" size="48" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && currentCityItems?.length === 0" class="empty-state">
              <van-icon name="photo-o" size="48" />
              <p>暂无收藏品数据</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>

  <!-- 卡片详情弹窗 -->
  <van-popup
  v-if="selectedCard"
    v-model:show="showCardDetailDialog"
    position="center"
    closeable
    :close-icon="''"
    :overlay-style="{
      'background-image': `url(${selectedCard.rarity == 'rare'?'/img/page_icons/card_detail_bg_fx_rare.png':(selectedCard.rarity == 'epic'?'img/page_icons/card_detail_bg_fx_epic.png':'/img/page_icons/card_detail_bg_fx_common.png')}), url(${selectedCard.rarity == 'rare'?'img/page_icons/card_details_bg_fxb_rare.png':selectedCard.rarity == 'epic'?'img/page_icons/card_details_bg_fxb_epic.png':'/img/page_icons/card_details_bg_fxb_common.png'})!important`,
      'background-position': 'top center, bottom center!important',
      'background-color': 'rgb(0, 0, 0)!important',
      'background-repeat': 'no-repeat, no-repeat!important',
      'background-size': '408px auto, 412px auto!important'
    }"
    round
    class="card-detail-popup"
  >
    <div class="card-detail-container" :class="selectedCard.rarity == 'rare'?'rare':(selectedCard.rarity == 'epic'?'epic':'')" >
      

      <div class="card-detail-image">
        <div class="exp">
          <p class="exp-num">
            {{ selectedCard.exp }}
          </p>
          <p class="exp-text">{{t('game.exp')}}</p>
        </div>
        <img :src="selectedCard.image_url" :alt="selectedCard.name" />
        <div class="card-detail-header">
          <h3>{{ selectedCard.name }}</h3>
        </div>
      </div>
      <div class="card-detail-info">
          <p>{{ selectedCard.description || '这是一张珍贵的收藏卡片，记录着城市的历史与文化。' }}</p>

      </div>
    </div>
  </van-popup>
</template>
<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { showToast, showFailToast } from 'vant'
import api from '@/utils/api'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
const authStore = useAuthStore()
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  currentCityId: {
    type: String,
    default: 'beijing'
  }
})

const emit = defineEmits(['update:show', 'city-selected', 'openMonumentQuiz'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})
const { t } = useI18n()
// 响应式数据
const loading = ref(false)
const cityCollections = ref({}) // 存储各城市的收藏品数据
const showCardDetailDialog = ref(false) // 卡片详情弹窗显示状态
const selectedCard = ref(null) // 选中的卡片

// 城市信息映射
const cityInfo = ref({
  beijing: {
    id: 'beijing',
    name: '北京',
    image: '/img/page_icons/beijing_cities.png',
    guard: {
      name: 'ZHOURUNFA.LI',
      title: '文化守卫',
      avatar: '/img/page_icons/beijing_cities.png',
      level: 6,
      expPerHour: 150
    }
  },
  vatican: {
    id: 'vatican',
    name: '梵蒂冈',
    image: '/img/page_icons/vatican_cities.png',
    guard: {
      name: 'ZHOURUNFA.LI',
      title: '文化守卫',
      avatar: '/img/page_icons/vatican_cities.png',
      level: 6,
      expPerHour: 150
    }
  }
})


// 计算属性
const currentCity = computed(() => {
  return cityInfo.value[props.currentCityId] || cityInfo.value.beijing
})

const currentCityGuard = computed(() => {
  const city = currentCity.value
  return city?.guard || {
    name: '未知守卫',
    avatar: '/img/page_icons/default_cities.png',
    level: 1,
    goldPerHour: 50,
    diamondPerHour: 1
  }
})

const currentCityItems = computed(() => {
  const cityId = props.currentCityId
  if (!cityId || !cityCollections.value[cityId]) {
    return []
  }
  return cityCollections.value[cityId].items || []
})

// 方法
const loadCityCollections = async (cityId) => {
  // if (cityCollections.value[cityId]) {
  //   return // 已加载过
  // }

  try {
    loading.value = true
    
    // 调用API获取用户收集的图鉴
    const response = await api.cultural.getUserArtifacts({
      city_id: cityId,
      limit: 50
    })
    
    let collectionItems = []
    
    if (response.data && response.data.data && response.data.data.artifacts) {
      const artifacts = response.data.data.artifacts
      
      // 转换数据格式以适配现有的UI结构
      collectionItems = await Promise.all(artifacts.map(async (artifact) => {
        // 检查是否已完成问答
        let hasCompletedQuiz = false
        let totalExperience = 0
        
        // try {
        //   const historyResponse = await api.cultural.getArtifactQuizHistory(artifact.artifact_code)
        //   if (historyResponse.data && historyResponse.data.data && historyResponse.data.data.has_completed) {
        //     hasCompletedQuiz = true
        //     totalExperience = historyResponse.data.data.quiz_session?.total_experience || 0
        //   }
        // } catch (error) {
        //   // 如果获取历史失败，说明可能没有完成问答
        //   hasCompletedQuiz = false
        // }
        
        return {
          id: artifact.artifact_code,
          item_id: artifact.id,
          artifact_code: artifact.artifact_code,
          rarity: artifact.rarity || 'common',
          name: artifact.name,
          owned: true, // 用户已收集的图鉴
          image_url: artifact.image_url || '/img/page_icons/vatican_card1.png',
          exp: totalExperience,
          description: artifact.description || '这是一张珍贵的文化图鉴，记录着城市的历史与文化。',
          isFlipping: false,
          hasCompletedQuiz: hasCompletedQuiz,
          category: artifact.category,
          obtained_at: artifact.obtained_at
        }
      }))
    }
    
    cityCollections.value[cityId] = {
      items: collectionItems,
      totalItems: collectionItems.length,
      collectedCount: collectionItems.length,
      completionRate: collectionItems.length > 0 ? 100 : 0
    }
    
  } catch (error) {
    console.error(`加载城市 ${cityId} 收藏品失败:`, error)
    showFailToast('加载收藏品失败')
    cityCollections.value[cityId] = {
      items: [],
      totalItems: 0,
      collectedCount: 0,
      completionRate: 0
    }
  } finally {
    loading.value = false
  }
}


const getRarityClass = (rarity) => {
  return `rarity-${rarity}`
}


const showItemDetail = (item) => {
  if (!item.owned) {
    showToast('尚未收集此物品')
    return
  }

  // 触发翻牌效果
  item.isFlipping = true

  // 延迟显示详情，等待翻牌动画
  setTimeout(() => {
    item.isFlipping = false
    showCardDetail(item)
  }, 600)
}

// 显示卡片详情弹窗
const showCardDetail = (item) => {
  selectedCard.value = item
  // showCardDetailDialog.value = true

  // 传递图鉴信息，包括是否已完成问答
  emit('openMonumentQuiz', {
    artifactId: item.artifact_code || item.id,
    artifactName: item.name,
    hasCompletedQuiz: item.hasCompletedQuiz,
    viewMode: item.hasCompletedQuiz // 如果已完成问答，则进入查看模式
  })
}


// 监听弹窗显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 加载当前城市的收藏品
    loadCityCollections(props.currentCityId)
  }
})

// 监听城市ID变化
watch(() => props.currentCityId, (newCityId) => {
  if (props.show && newCityId) {
    loadCityCollections(newCityId)
  }
})

// 组件挂载时加载默认城市
onMounted(() => {
  if (props.show) {
    loadCityCollections(props.currentCityId)
  }
})
</script>

<style lang="scss" scoped>
.city-collection-popup{
  
  overflow-y: visible;
  :deep(.van-popup__close-icon--top-right){
  top: -16px!important;
  right: -16px!important;
  .van-icon__image{
      width: 66px;
      height: 70px;

    }
  
}
}
.city-collection-container {
  // padding: 20px;
  background: url('/img/page_icons/cards_bg.png') no-repeat center center;
  background-size: 100% 100%;
  border: 4px solid #616881;
  border-radius: 10px;
  color: white;
  width: 684px;
  height: 1180px;
  display: flex;
  flex-direction: column;
  
}

.collection-header {
  background: url('/img/page_icons/cities_card_title.png') no-repeat center center;
  background-size: 100% 100%;
  width: 556px;
  height: 102px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  margin-top: -20px;
}



.city-selector {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}


.city-collections {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .description{
    font-size: 24px;
    color: #fff;
    width: 80%;
    margin: 40px auto;
    text-align: center;
  }
}


.collections-grid {
  // flex: 1;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
  overflow-y: auto;
  margin-top: 20px;
}

.collection-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  perspective: 1000px;
  width: 140px;
  height: 226px;
  background: url('/img/page_icons/collection_item_bg.png') no-repeat center center;
  background-size: 100% 100%;

  &:hover {
    transform: translateY(-2px);
  }
// 卡片翻转容器
.card-container {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.6s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &.flip-animation {
      transform: rotateY(180deg);
    }
  }

  .card-front,
  .card-back {
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .card-front {
    z-index: 2;
  }

  .card-back {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: rotateY(180deg);
    // background: linear-gradient(135deg, #6e7593 0%, #4c43c6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
  &.rare {
    background: url('/img/page_icons/collection_item_bg_rare.png') no-repeat center center;
    background-size: 100% 100%;
    .card-back{
      // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
  &.epic {
    background: url('/img/page_icons/collection_item_bg_epic.png') no-repeat center center;
    background-size: 100% 100%;
    .card-back{
      // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  &.locked {
    .item-info {
      opacity: 0.6;
    }
  }

  // 问答完成状态样式
  &.quiz-completed {
    .item-frame {
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        top: -3px;
        right: -3px;
        bottom: -3px;
        left: -3px;
        border: 2px solid #4CAF50;
        border-radius: 50%;
        animation: quiz-completed-glow 2s ease-in-out infinite alternate;
      }
    }
  }
}



.card-back-content {
  text-align: center;
}

.item-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 98px;
  height: 98px;
  border-radius: 50%;
  transition: all 0.3s ease;
  margin-top: 20px;
  overflow: hidden;
  background: url('/img/page_icons/collection_item_img_bg.png') no-repeat center center;
  background-size: 100% 100%;

  // 稀有度边框样式
  &.rarity-common {
    // border-color: #9e9e9e;
    // box-shadow: 0 0 10px rgba(158, 158, 158, 0.5);
  }

  &.rarity-rare {
    // border-color: #2196f3;
    // box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
  }

  &.rarity-epic {
    // border-color: #9c27b0;
    // box-shadow: 0 0 10px rgba(156, 39, 176, 0.5);
     animation: rarity-glow 2s ease-in-out infinite alternate;
  }

}

@keyframes rarity-glow {
  from {
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.7);
  }
  to {
    box-shadow: 0 0 25px rgba(255, 152, 0, 1);
  }
}

@keyframes quiz-completed-glow {
  from {
    border-color: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
  }
  to {
    border-color: #66BB6A;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
  }
}

// 问答完成状态指示器样式
.quiz-completed-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border: 2px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: quiz-badge-pulse 2s ease-in-out infinite;
  
  .van-icon {
    font-size: 12px;
  }
}

@keyframes quiz-badge-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.item-image {
  width: 86%;
  height: 86%;
  object-fit: cover;
  border-radius: 50%;
}

.item-locked {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.item-info {
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 26px;
}




.loading-state, .empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  color: #ccc;

  .van-icon {
    margin-bottom: 10px;
  }
}

// 卡片详情弹窗样式
.card-detail-popup {
  background: none;
  // :deep(.van-popup) {
  //   background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  //   border: 3px solid #FFD700;
  //   max-width: 400px;
  //   width: 90%;
  // }
}

.card-detail-container {
  padding: 50px;
  width: 692px;
  height: 928px;
  background: url('/img/page_icons/common_card_details_bg.png') no-repeat center center;
  background-size: 100% 100%;;
   .card-detail-image {
    position: relative;
    width: 100%;
    height: 60%;
      .exp{
          position: absolute;
          top: -2px;
          left: 0;
          width: 80px;
          height: 140px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fff;
          border-top-left-radius: 20px;
          border-bottom-right-radius: 40px;
          background-color: rgb(172, 172, 171);
          color: #fff;
          font-size: 24px;
          font-weight: bold;
          .exp-num{

          }
      }
      .card-detail-header {
        position: absolute;
        left: 0;
        bottom: 0;
        background: #8F8F8E;
        padding: 10px;
        border-top-right-radius: 20px;
        color: #fff;
        h3 {
          font-size: 24px;
        }

      }
      img {
        width: 100%;
        height: 100%;
        object-fit: fill;
      }
  }

  .card-detail-info {
    background-color: rgb(215, 215, 215);
    color: #313131;
    padding: 10px;
    border-bottom-right-radius: 24px;
    border-bottom-left-radius: 24px;
    height: 40%;
    overflow-y: auto;
      p {
        margin: 0;
        line-height: 1.5;
      }
  
  }
  &.rare {
    background: url('/img/page_icons/rare_card_details_bg.png') no-repeat center center;
    background-size: 100% 100%;
    .card-detail-header, .exp {
        background-color: rgb(56, 87, 180);
    }
    .card-detail-info {
      background-color: rgb(153, 188, 236);
    }
  }
  &.epic {
    background: url('/img/page_icons/epic_card_details_bg.png') no-repeat center center;
    background-size: 100% 100%;
    .card-detail-header, .exp{
      background-color: rgb(153, 56, 180);
    }
    .card-detail-info {
      background-color: rgb(214, 153, 236);
    }
  }
 
}





</style>