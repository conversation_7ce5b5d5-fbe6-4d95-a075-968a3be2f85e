<template>
  <div v-if="show" class="monument-quiz-overlay">
    <div class="monument-quiz-container">
      <!-- 顶部横幅标题 -->
      <div class="title-banner" v-if="!isRestored && !showResult && !quizStarted">
        <div class="banner-ribbon">
          <h1 class="banner-title">{{ currentMonument?.name || '文化问答' }}</h1>
          
        </div>
      </div>

      <!-- 古迹卡片 -->
      <div class="monument-card" v-if="!isRestored && !showResult && !quizStarted">
        <div class="card-frame" >
         
          <div class="card-inner" :style="{ backgroundImage: currentMonument?.pollutedImage ? `url(${currentMonument.pollutedImage})` : 'none' }">
            <div class="card-border">
              <div class="experience-badge" v-if="!isRestored">
                +{{ successReward }}声望
              </div>
              <div class="card-title">{{ currentMonument?.name || '文化问答' }}</div>
            </div>
          
            
          </div>
        </div>
      </div>

      <!-- 国王角色对话 - 挑战前 -->
      <div class="king-dialogue before-quiz" v-if="!isRestored && !showResult && !quizStarted">
        <div class="king-avatar">
          <img src='/img/page_icons/quiz_rubbish_king.png'>
        </div>
        <div class="dialogue-bubble">
          <p class="king-text">
            嘿！别以为抢回去就是你的了！这件宝贝的故事你懂多少？敢不敢回答我的问题，证明你不是个草包？
          </p>
        </div>
      </div>

      <!-- 挑战按钮 -->
      <div class="challenge-section" v-if="!isRestored && !showResult && !quizStarted">
        <button class="main-challenge-btn" @click="startQuiz">
          {{ props.viewMode ? '查看' : '挑战' }}
        </button>
      </div>

      <!-- 问答区域 -->
      <div class="quiz-section" v-if="!isRestored && !showResult && quizStarted">
        <div class="question-container">
          <div class="question-card">
            <img src="/img/page_icons/rubbish_king_icon.png" class="question-mark">
            <div class="question-content">
              <h3 class="question-text">{{ currentQuestion.question }}</h3>

              <div class="options-container">
                <button
                  v-for="(option, index) in currentQuestion.options"
                  :key="index"
                  class="option-btn"
                  :class="{
                    'selected': selectedAnswer === index && !props.viewMode,
                    'correct': (showAnswerResult || props.viewMode) && index === currentQuestion.correctAnswer,
                    'wrong': showAnswerResult && selectedAnswer === index && index !== currentQuestion.correctAnswer && !props.viewMode
                  }"
                  @click="selectAnswer(index)"
                  :disabled="showAnswerResult || props.viewMode"
                >
                  <div class="option-label">{{ String.fromCharCode(65 + index) }}</div>
                  <div class="option-text">{{ option }}</div>
                </button>
              </div>

              <div class="explanation" v-if="(showAnswerResult || props.viewMode) && currentQuestion.explanation">
                <p><strong>解析：</strong>{{ currentQuestion.explanation }}</p>
              </div>
            </div>
            <div class="question-header">
              <div class="question-progress">{{ currentQuestionIndex + 1 }}/{{ totalQuestions }}</div>
            </div>
          </div>

          <div class="quiz-actions">
            <!-- 查看模式：只显示下一题按钮 -->
            <template v-if="props.viewMode">
              <button
                class="next-btn"
                @click="nextQuestion"
              >
                {{ currentQuestionIndex < totalQuestions - 1 ? '下一题' : '完成' }}
              </button>
            </template>
            <!-- 答题模式：显示提交答案和下一题按钮 -->
            <template v-else>
              <button
                v-if="!showAnswerResult && selectedAnswer !== null"
                class="submit-answer-btn"
                @click="submitAnswer"
              >
                提交答案
              </button>
              <button
                v-if="showAnswerResult"
                class="next-btn"
                @click="nextQuestion"
              >
                {{ currentQuestionIndex < totalQuestions - 1 ? '下一题' : '完成' }}
              </button>
            </template>
          </div>
        </div>
      </div>

      <!-- 奖励弹框 -->
      <div class="reward-modal" v-if="showResult">
        <div class="reward-container">
          <!-- 顶部横幅 -->
          <div class="reward-banner">
            <div class="banner-ribbon">
              <h2 class="banner-title">声望得分</h2>
            </div>
          </div>

          <!-- 国王对话 -->
          <div class="king-dialogue">
            <div class="dialogue-bubble">
              <p class="king-text">
                是不是感觉自己的文化知识已经被我彻底碾碎了？哈哈哈！承认吧，
              </p>
            </div>
          </div>

          <!-- 奖励卡片 -->
          <div class="reward-card">
            <img src="/img/page_icons/rubbish_king_icon.png" class="reward-title-icon">
            <div class="reward-title">守护声望：{{ finalReward }}</div>
            <div class="reward-subtitle">答对题目：{{ correctAnswers }}/{{ totalQuestions }}</div>

            <div class="reward-actions">
              <div class="get-btn" @click="claimReward(false)">
                Get
              </div>
              <div class="double-btn" @click="claimReward(true)">
              </div>
            </div>
          </div>

          <!-- 返回按钮 -->
          <button class="return-btn" @click="closeQuiz">
            返回
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useGameStore } from '@/stores/game'
import { api } from '@/utils/api'
import { showFailToast } from 'vant'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  monumentId: {
    type: String,
    default: 'monument_1'
  },
  quizType: {
    type: String,
    default: 'monument', // 'monument' 或 'artifact'
    validator: (value) => ['monument', 'artifact'].includes(value)
  },
  difficulty: {
    type: String,
    default: 'medium'
  },
  viewMode: {
    type: Boolean,
    default: false // true表示查看模式，不能提交答案
  }
})

const emit = defineEmits(['close', 'success', 'failure'])

const gameStore = useGameStore()

// 古迹数据
const monuments = ref({})

// 游戏状态
const quizStarted = ref(false)
const currentQuestionIndex = ref(0)
const selectedAnswer = ref(null)
const showAnswerResult = ref(false)
const showResult = ref(false)
const isRestored = ref(false)
const correctAnswers = ref(0)
const successReward = ref(30)
const failurePenalty = ref(10)
const isLoading = ref(false)
const questions = ref([])
const sessionId = ref('')
const userAnswers = ref([])

// 计算属性
const currentMonument = computed(() => {
  // 提供默认的古迹数据
  const defaultMonument = {
    name: '文化问答',
    pollutedImage: '/img/page_icons/vatican_cities.png',
    cleanImage: '/img/page_icons/default_monument_clean.png',
    questions: []
  }
  return monuments.value[props.monumentId] || defaultMonument
})

const currentQuestion = computed(() => {
  return questions.value[currentQuestionIndex.value] || null
})

const totalQuestions = computed(() => {
  return questions.value.length || 0
})

const finalReward = computed(() => {
  return successReward.value
})

const isSuccess = computed(() => {
  const total = totalQuestions.value
  return total > 0 ? correctAnswers.value >= Math.ceil(total * 0.6) : false // 60%正确率算成功
})

// 监听显示状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    resetQuiz()
    loadQuestions()
  }
})

// 加载问答题目
const loadQuestions = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  try {
    // 生成会话ID
    sessionId.value = `quiz_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    
    let response
    
    if (props.quizType === 'artifact') {
      // 文化图鉴问答：使用统一接口，后端自动判断返回历史记录或新题目
      response = await api.cultural.getArtifactQuestions(props.monumentId, {
        count: 5,
        difficulty: props.difficulty
      })

      // 检查是否返回的是历史记录
      if (response.data && response.data.data && response.data.data.has_completed && response.data.data.questions_and_answers) {
        // 历史记录模式
        questions.value = response.data.data.questions_and_answers.map(q => ({
          id: q.quiz_id,
          question: q.question,
          options: q.options,
          correctAnswer: q.correct_answer ? q.correct_answer.charCodeAt(0) - 65 : 0,
          difficulty: q.difficulty,
          category: q.category,
          explanation: q.explanation,
          userAnswer: q.user_answer,
          isCorrect: q.is_correct
        }))

        // 设置会话统计信息
        const sessionInfo = response.data.data.quiz_session
        if (sessionInfo) {
          correctAnswers.value = sessionInfo.correct_answers
          successReward.value = sessionInfo.total_experience
        }
      } else if (response.data && response.data.data && response.data.data.questions) {
        // 新题目模式
        questions.value = response.data.data.questions.map(q => ({
          id: q.quiz_id,
          question: q.question,
          options: q.options,
          // 不接收正确答案，保持神秘感
          difficulty: q.difficulty,
          category: q.category,
          explanation: ''  // 将在提交后获得
        }))
      } else {
        throw new Error('题目数据格式错误')
      }
    } else {
      // 古迹问答：使用原有逻辑
      if (props.viewMode) {
        // 古迹问答暂时没有历史记录功能，直接加载新题目
        await loadNewQuestions()
        return
      } else {
        // 答题模式：加载新题目
        await loadNewQuestions()
      }
    }
    
    console.log('加载了', questions.value.length, '道题目')
  } catch (error) {
    console.error('加载问答题目失败:', error)
    showFailToast('加载题目失败，使用默认题目')
    // 如果API失败，继续使用静态题目
    questions.value = []
  } finally {
    isLoading.value = false
  }
}

// 加载新题目（答题模式）
const loadNewQuestions = async () => {
  let response
  if (props.quizType === 'artifact') {
    // 调用文化图鉴问答API
    response = await api.cultural.getArtifactQuestions(props.monumentId, {
      count: 5,
      difficulty: props.difficulty
    })
  } else {
    // 调用古迹问答API
    response = await api.cultural.getMonumentQuestions(props.monumentId, {
      count: 5,
      difficulty: props.difficulty
    })
  }
  
  if (response.data && response.data?.data?.questions) {
    questions.value = response.data.data.questions.map(q => ({
      id: q.quiz_id,
      question: q.question,
      options: q.options,
      // 不接收正确答案，保持神秘感
      difficulty: q.difficulty,
      category: q.category,
      explanation: ''  // 将在提交后获得
    }))
  } else {
    throw new Error('题目数据格式错误')
  }
}

// 重置问答
const resetQuiz = () => {
  quizStarted.value = false
  currentQuestionIndex.value = 0
  selectedAnswer.value = null
  showAnswerResult.value = false
  showResult.value = false
  isRestored.value = false
  correctAnswers.value = 0
  userAnswers.value = []
}

// 开始问答
const startQuiz = () => {
  quizStarted.value = true
  
  // 如果是查看模式，直接显示答案结果
  if (props.viewMode) {
    showAnswerResult.value = true
  }
}

// 选择答案
const selectAnswer = (index) => {
  if (showAnswerResult.value || props.viewMode) return
  selectedAnswer.value = index
}

// 提交答案
const submitAnswer = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  // 记录用户答案
  const answerData = {
    quiz_id: currentQuestion.value.id,
    user_answer: String.fromCharCode(65 + selectedAnswer.value), // 转换为A、B、C、D
    answer_time: 30 // 可以记录实际答题时间
  }
  
  try {
    let response
    if (props.quizType === 'artifact') {
      // 提交文化图鉴问答答案
      response = await api.cultural.submitArtifactAnswers(props.monumentId, {
        answers: [answerData], // 单题提交
        session_id: sessionId.value
      })
    } else {
      // 提交古迹问答答案
      response = await api.cultural.submitMonumentAnswers(props.monumentId, {
        answers: [answerData], // 单题提交
        session_id: sessionId.value
      })
    }
    
    if (response.data && response.data.data.detailed_results && response.data.data.detailed_results.length > 0) {
      const result = response.data.data.detailed_results[0]
      
      // 更新当前问题的答案状态
      currentQuestion.value.userAnswer = answerData.user_answer
      currentQuestion.value.isCorrect = result.is_correct
      currentQuestion.value.explanation = result.explanation || currentQuestion.value.explanation
      
      // 设置正确答案的索引(将A, B, C, D转换为0, 1, 2, 3)
      if (result.correct_answer) {
        currentQuestion.value.correctAnswer = result.correct_answer.charCodeAt(0) - 65
      }
      
      // 累计正确答案数
      if (result.is_correct) {
        correctAnswers.value++
      }
      
      // 显示答案结果
      showAnswerResult.value = true
      
      // 记录答案以备最终统计
      userAnswers.value.push({
        ...answerData,
        is_correct: result.is_correct,
        explanation: result.explanation
      })
      
    } else {
      showFailToast('提交答案失败，请重试')
    }
    
  } catch (error) {
    console.error('提交答案失败:', error)
    showFailToast('提交答案失败，请重试')
  }
  
  isLoading.value = false
}


// 下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < totalQuestions.value - 1) {
    currentQuestionIndex.value++
    selectedAnswer.value = null
    
    // 查看模式下始终显示答案结果
    if (props.viewMode) {
      showAnswerResult.value = true
    } else {
      showAnswerResult.value = false
    }
  } else {
    // 所有题目完成
    if (props.viewMode) {
      // 查看模式直接关闭
      closeQuiz()
    } else {
      // 答题模式显示结果
      finishQuiz()
    }
  }
}

// 完成问答
const finishQuiz = () => {
  showResult.value = true
  
  if (isSuccess.value) {
    isRestored.value = true
    gameStore.protectMonument(true)
    emit('success', {
      monumentId: props.monumentId,
      score: correctAnswers.value,
      total: totalQuestions.value
    })
  } else {
    gameStore.protectMonument(false)
    emit('failure', {
      monumentId: props.monumentId,
      score: correctAnswers.value,
      total: totalQuestions.value
    })
  }
}

// 领取奖励
const claimReward = (isDouble) => {
  const finalReward = isDouble ? successReward.value * 2 : successReward.value
  
  if (isDouble) {
    playRewardAd(() => {
      gameStore.addCivilizationExp(finalReward)
      closeQuiz()
    })
  } else {
    // 已经在protectMonument中给过奖励了，这里不重复给
    closeQuiz()
  }
}


// 关闭问答
const closeQuiz = () => {
  emit('close')
}

// 播放奖励广告
const playRewardAd = (callback) => {
  if (window.CrazyGames && window.CrazyGames.SDK) {
    const adCallbacks = {
      adFinished: () => {
        console.log("广告播放完成")
        callback()
      },
      adError: (error) => {
        console.log("广告播放失败", error)
        callback()
      },
      adStarted: () => console.log("开始播放广告")
    }
    window.CrazyGames.SDK.ad.requestAd("rewarded", adCallbacks)
  } else {
    setTimeout(callback, 1000)
  }
}
</script>

<style lang="scss" scoped>
.monument-quiz-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #ff69b4 0%, #87ceeb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.5s ease-in-out;
}

.monument-quiz-container {
  background: url('/img/page_icons/quiz_bg.png') no-repeat center center;
  background-size: 100% 100%;
  border-radius: 20px;
  padding: 20px;
  height: 1260px;
  width: 804px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 顶部横幅标题
.title-banner {
  margin-bottom: 20px;

  .banner-ribbon {
    width: 674px;
    height: 172px;

    background: url('/img/page_icons/quiz_card_name.png') no-repeat center center;
    background-size:100% 100%;
    padding: 15px 30px;
    position: relative;

    .banner-title {
      color: #8b4513;
      font-size: 28px;
      font-weight: bold;
      padding-top: 30px;
      text-align: center;
      text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
    }

    
  }
}

// 古迹卡片
.monument-card {
  margin-bottom: 20px;
  margin-top: -180px;
  margin-right: -200px;
  .card-frame {
    width: 567px;
    height: 771px;
    background: url('/img/page_icons/quiz_border1.png') no-repeat center center;
    background-size: 100% 100%;
    margin-left:auto;
    display:flex;
    align-items: center;
    justify-content: center;
    transform: rotate(10deg);
    
    .card-inner {
      // background: url('/img/page_icons/quiz_card_bg1.png') no-repeat center center;
    background-size: 100% 100%;
      // text-align: center;
      // margin-top: -100px;
      width: 404px;
      height: 508px;
      margin-top: 90px;
      .card-border{
        position: relative;
        width: 100%;
        height: 100%;
        background: url('/img/page_icons/quiz_pic_border1.png') no-repeat center center;
        background-size: 100% 100%;
        .card-title {
          position: absolute;
          bottom: 34px;
          left: 0;
          width: 100%;
          text-align: center;
          font-size: 26px;
          color: #8b4513;
          font-weight: bold;
        }
        .experience-badge {
          position: absolute;
          bottom: 82px;
          left: 50%;
          transform: translateX(-50%);
          background: rgb(120, 120, 120);
          color: white;
          padding: 8px 12px;
          border-radius: 12px;
          font-size: 24px;
          font-weight: bold;
        }
      }

     
    }
  }
}

// 国王对话
.king-dialogue.before-quiz {
  z-index: 1;
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  margin-top: -300px;

  .king-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    img{
      width: 290px;
    }

   
  }

  .dialogue-bubble {
    flex: 1;
    align-self: flex-end;
    width: 503px;
    height: 259px;
    background: url('/img/page_icons/quiz_boss_dialauge.png') no-repeat center center;
    background-size: 100% 100%;
    border-radius: 20px;
    padding: 30px;
    padding-top: 70px;
    margin-left: -70px;
    position: relative;
    .king-text {
      height: 170px;
      overflow-y: auto;
      color: #333;
      font-size: 24px;
      line-height: 1.4;
      font-weight: 500;
    }
  }
}

// 挑战按钮区域
.challenge-section {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 40px;

  .main-challenge-btn {
    padding: 15px 50px;
    background: url('/img/page_icons/quiz_btn.png') no-repeat center center;
    background-size: 100% 100%;
    width: 168px;
    height: 78px;
    color: white;
    border: none;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;

  }
}

// 问答区域
.quiz-section {
  width: 100%;
  margin-bottom: 20px;

  .question-container {
    // 散发着光芒的问答框
    .question-card {
      position: relative;
      .question-mark{
        position: absolute;
        top: -140px;
        right: -10px;
        width: 222px;
        height: 208px;
      }
      background: rgb(217, 217, 217);
      padding: 40px;
      padding-top: 60px;
    border-radius: 40px;
    box-shadow: 
    0 0 30px #fff,            /* 内层白光 */
    0 0 30px rgba(255,255,255,0.8), /* 中层光 */
    0 0 30px rgba(255,255,255,0.5); /* 外层光 */

      margin-bottom: 30px;

      .question-header {
        text-align: center;
        margin-bottom: 15px;

        .question-progress {
          background: #666;
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 20px;
          display: inline-block;
        }
      }

      .question-content {
        .question-text {
          font-size: 24px;
          color: #333;
          margin-bottom: 20px;
          text-align: left;
          line-height: 1.5;
          font-weight: 500;
        }

        .options-container {
          display: flex;
          flex-direction: column;
          gap: 20px;
          margin-bottom: 15px;

          .option-btn {
            display: flex;
            align-items: center;
            background: rgb(191, 189, 189);
            border: 2px solid #ddd;
            border-radius: 24px;
            color: #333;
            font-size: 24px;
            height: 80px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;

            .option-label {
              background: #666;
              color: white;
              width: 80px;
              border-top-left-radius: 24px;
              border-bottom-left-radius: 24px;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 22px;
              font-weight: bold;
              margin-right: 12px;
              flex-shrink: 0;
            }

            .option-text {
              padding: 20px 30px;
              flex: 1;
            }

            &:hover:not(:disabled) {
              background: #e8f4fd;
              border-color: #2196F3;
              transform: translateY(-1px);
            }

            &.selected {
              border-color: #ff5722;
              background: #fff3e0;

              .option-label {
                background: #ff5722;
              }
            }

            &.correct {
              border-color: #4CAF50;
              background: #e8f5e8;

              .option-label {
                background: #4CAF50;
              }
            }

            &.wrong {
              border-color: #f44336;
              background: #ffebee;

              .option-label {
                background: #f44336;
              }
            }

            &:disabled {
              cursor: not-allowed;
            }
          }
        }

        .explanation {
          // background: #f0f8ff;
          // border: 1px solid #b3d9ff;
          // border-radius: 24px;
          padding: 24px;
          margin-top: 10px;

          p {
            margin: 0;
            font-size: 22px;
            color: #078330;
            line-height: 1.4;
          }
        }
      }
    }

    .quiz-actions {
      display: flex;
      justify-content: center;

      .submit-answer-btn, .next-btn {
        padding: 12px 40px;
        border: none;
        border-radius: 25px;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .submit-answer-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #f57c00, #ef6c00);
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
      }

      .next-btn {
        background: url('/img/page_icons/quiz_btn.png') no-repeat center center;
        background-size: 100% 100%;
        width: 168px;
        height: 78px;
        color: white;
        text-shadow: 0 6px 10px rgba(0, 0, 0, 0.7);
        font-size: 28px;
        font-weight: bold;

        &:hover {
          background: linear-gradient(135deg, #f57c00, #ef6c00);
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}

.result-section {
  text-align: center;
  
  .result-container {
    padding: 30px;
    border-radius: 20px;
    
    &.success {
      background: linear-gradient(135deg, #1a4a1a, #2a6a2a);
      border: 3px solid #4CAF50;
    }
    
    &.failure {
      background: linear-gradient(135deg, #4a1a1a, #6a2a2a);
      border: 3px solid #f44336;
    }
    
    .result-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    
    .result-title {
      font-size: 24px;
      color: #fff;
      margin-bottom: 20px;
    }
    
    .result-stats {
      margin-bottom: 30px;
      
      p {
        color: #ccc;
        font-size: 26px;
        margin: 10px 0;
      }
      
      .reward-text {
        color: #4CAF50;
        font-weight: bold;
      }
      
      .penalty-text {
        color: #f44336;
        font-weight: bold;
      }
    }
    
    .result-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
      
      .reward-btn, .retry-btn, .close-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 26px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
      }
      
      .normal-reward {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
      }
      
      .double-reward {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
        
        &:after {
          content: "🎬";
          margin-left: 8px;
        }
      }
      
      .retry-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
      }
      
      .close-btn {
        background: linear-gradient(135deg, #607D8B, #455A64);
        color: white;
      }
    }
  }
}

// 奖励弹框
.reward-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #ff69b4 0%, #87ceeb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;

  .reward-container {
    background: transparent;
    border-radius: 20px;
    padding: 20px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .reward-banner {
      width: 100%;
      margin-bottom: 20px;

      .banner-ribbon {
        width: 674px;
        height: 172px;
        margin: 0 auto;
        background: url('/img/page_icons/quiz_card_name.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .banner-title {
          color: #8b4513;
          font-size: 24px;
          font-weight: bold;
          padding-bottom: 30px;
          text-align: center;
          text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
        }
      }
    }

    .king-dialogue {
      margin-bottom: 20px;
      
      

      .dialogue-bubble {
        width: 450px;
        height: 289px;
        background: url('/img/page_icons/quzi_boss_dialouge2.png') no-repeat center center;
        background-size: 100% 100%;
        padding: 30px;
        padding-top: 20px;
        margin-left: 150px;
        position: relative;
        .king-text {
          color: white;
          height: 160px;
          overflow-y: auto;
          font-size: 24px;
          line-height: 1.4;
          font-weight: 500;
        }
      }
    }

    .reward-card {
      position: relative;
      background: rgb(217, 217, 217);
      border-radius: 30px;
      padding: 25px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      margin-bottom: 20px;
      width: 600px;
      padding-top: 40px;
      .reward-title-icon{
        position: absolute;
        top: -110px;
        left: 20px;
        width: 200px;
        z-index: 100;
      }

      .reward-title {
        font-size: 36px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }

      .reward-subtitle {
        font-size: 24px;
        color: #666;
        margin-bottom: 20px;
      }

      .reward-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin: 30px auto;

        .get-btn {
          background: url('/img/page_icons/quiz_reward_get_btn.png') no-repeat center center;
          background-size: 100% 100%;
          width: 168px;
          height: 78px;
          color: white;
          font-size: 28px;
          font-weight: bold;
          text-align: center;
          line-height: 78px;
        }

        .double-btn {
          background: url('/img/page_icons/quiz_reward_double_btn.png') no-repeat center center;
          background-size: 100% 100%;
          width: 172px;
          height: 82px;
          color: white;

         
        }
      }
    }

    .return-btn {
      width: 320px;
      height: 122px;
      background: url('/img/page_icons/quiz_btn.png') no-repeat center center;
      background-size: 100% 100%;
      color: white;
      border: none;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;

    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes restore {
  from {
    filter: hue-rotate(120deg) saturate(0.5) brightness(0.7);
    transform: scale(0.9);
  }
  to {
    filter: none;
    transform: scale(1);
  }
}

@keyframes pollutionPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

</style>