<template>
  <div class="guest-login-container">
    <!-- <div class="guest-login-content"> -->
      <!-- <div class="guest-icon">
        👤
      </div>
      <h3>游客登录</h3>
      <p class="guest-desc">快速开始游戏，无需注册</p>
       -->
      <van-button 
        type="primary" 
        block 
        size="large"
        :loading="loading"
        @click="handleGuestLogin"
        class="guest-login-btn"
      >
        {{ loading ? '登录中...' : '游客登录' }}
      </van-button>
      
      <!-- <div class="guest-note">
        <p>游客模式下的游戏进度将保存在本设备</p>
        <p>建议使用社交账号登录以同步进度</p>
      </div> -->
    <!-- </div> -->
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { api } from '@/utils/api'
import { showToast, showFailToast, showLoadingToast, closeToast } from 'vant'

const emit = defineEmits(['success'])

const loading = ref(false)
const authStore = useAuthStore()

/**
 * 获取设备信息
 */
function getDeviceInfo() {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    screen: `${screen.width}x${screen.height}`,
    timestamp: new Date().toISOString()
  }
}

/**
 * 生成设备ID
 */
function getDeviceId() {
  let deviceId = localStorage.getItem('device_id')
  if (!deviceId) {
    deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    localStorage.setItem('device_id', deviceId)
  }
  return deviceId
}

/**
 * 处理游客登录
 */
async function handleGuestLogin() {
  try {
    loading.value = true
    
    showLoadingToast({
      message: '正在登录...',
      forbidClick: true,
      duration: 0
    })

    // 获取设备信息
    const deviceId = getDeviceId()
    const deviceInfo = getDeviceInfo()

    // 调用后端统一登录API进行游客登录
    const apiResponse = await api.auth.unifiedLogin('guest', {
      device_id: deviceId,
      device_info: deviceInfo
    })

    closeToast()

    if (apiResponse.data) {
      const { user_id, token, refresh_token, user_info } = apiResponse.data
      
      // 更新认证状态
      authStore.login({
        provider: 'guest',
        user: {
          user_id: user_id,
          nickname: user_info.nickname,
          avatar_url: user_info.avatar_url,
          level: user_info.level,
          exp: user_info.exp,
          vip_level: user_info.vip_level,
          total_play_time: user_info.total_play_time,
          created_at: user_info.created_at
        },
        token: token,
        refreshToken: refresh_token
      })

      // 显示成功提示
      showToast({
        message: `欢迎，${user_info.nickname}！`,
        type: 'success'
      })

      // 触发成功事件
      emit('success')

      console.log('游客登录成功:', user_info.nickname)
    }
  } catch (error) {
    closeToast()
    console.error('游客登录失败:', error)
    
    let errorMessage = '登录失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    }
    
    showFailToast(errorMessage)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.guest-login-container {
  /* padding: 20px; */
  text-align: center;
}

.guest-login-content {
  /* background: #f8f9fa; */
  /* border-radius: 12px;
  padding: 30px 20px;
  border: 2px dashed #e0e0e0; */
}

.guest-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #333;
}

.guest-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 24px;
}

.guest-login-btn {
  margin-bottom: 20px;
  background: #4CAF50;
  border: none;
}

.guest-note {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.guest-note p {
  margin: 4px 0;
}
</style>
