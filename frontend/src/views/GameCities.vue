<template>
  <div class="game-level-page">
    <div class="ranking-button">
      <img src="/img/page_icons/ranking_1.png" alt="" class="rank-icon">
      <span class="ranking-text">Ranking</span>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading size="24px" color="white">加载中...</van-loading>
    </div>

    <!-- 城市列表 -->
    <div v-else class="level-list">
      <div
        v-for="(city, index) in cities"
        :key="city.id"
        class="level-item"
        @click="goToCity(city)"
        :class="[
          `theme-${index % 2 === 0 ? 'purple' : 'blue'}`,
          { 'locked': !city.is_unlocked }
        ]"
      >
        <div class="level-name-tag">{{ city.name }}</div>

        <div class="level-info">
          <van-image
            class="level-thumbnail"
            :src="city.thumbnail || city.image"
            width="60"
            height="60"
            fit="cover"
          />
          <van-icon
            v-if="city.earned_stars === city.total_stars && city.total_stars > 0"
            name="trophy"
            class="trophy-icon"
            size="24"
          />
        </div>

        <!-- 星级进度 -->
        <div class="level-stars">
          <div 
            v-for="(star, s_index) in getCityStars(city)" 
            :key="s_index" 
            class="star-slot" 
            @click="(event) => handleStarClick(star, city, event)"
          >
            <img 
              v-if="star.achieved"  
              src="/img/page_icons/game_handled_star.png" 
              class="star-background star-filled"  
              alt=""
            >
            <img 
              v-else 
              src="/img/page_icons/game_handing_star.png" 
              alt=""  
              class="star-background star-outline"
            >
            <img :src="star.icon" class="inner-icon" alt="">
          </div>
        </div>

        <!-- 解锁提示 -->
        <div v-if="!city.is_unlocked" class="unlock-overlay">
          <van-icon name="lock" size="32" color="white" />
          <p>需要等级 {{ getCityUnlockLevel(city) }}</p>
        </div>
      </div>
    </div>

    <!-- 重置确认对话框 -->
    <van-dialog
      v-model:show="showResetDialog"
      title="重新开始游戏"
      :message="resetDialogMessage"
      show-cancel-button
      confirm-button-text="确认重置"
      cancel-button-text="取消"
      @confirm="confirmReset"
      @cancel="cancelReset"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Icon as VanIcon, Image as VanImage, Loading as VanLoading, Dialog as VanDialog, showToast, showSuccessToast, showFailToast } from 'vant'
import { useRouter } from 'vue-router'
import citiesService from '@/services/citiesService'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(true)
const cities = ref([])
const showResetDialog = ref(false)
const resetDialogMessage = ref('')
const pendingResetAction = ref(null)

// 页面初始化
onMounted(async () => {
  

  await loadCities()
})

// 加载城市列表
const loadCities = async () => {
  loading.value = true
  try {
    const result = await citiesService.getCitiesList()
    if (result.success) {
      cities.value = result.cities || []
      console.log('🏙️ 城市列表加载完成:', cities.value)
    } else {
      showFailToast(result.error || '加载城市列表失败')
    }
  } catch (error) {
    console.error('❌ 加载城市列表异常:', error)
    showFailToast('加载城市列表失败')
  } finally {
    loading.value = false
  }
}

// 点击城市卡片
const goToCity = (city) => {
  if (!city.is_unlocked) {
    showToast(`需要等级 ${getCityUnlockLevel(city)} 才能解锁此城市`)
    return
  }
  
  // 如果城市没有完成任何关卡，直接进入第一个关卡
  const nextLevel = citiesService.getNextIncompleteLevel(city)
  if (nextLevel) {
    startLevel(nextLevel.city_id, nextLevel.scene_id, nextLevel.type)
  } else {
    // 所有关卡都完成了，显示城市详情或提供重置选项
    showToast('所有关卡已完成！')
  }
}

// 处理星星点击
const handleStarClick = async (star, city, event) => {
  event.stopPropagation()
  
  if (!city.is_unlocked) {
    showToast(`需要等级 ${getCityUnlockLevel(city)} 才能解锁此城市`)
    return
  }
  
  console.log('🌟 点击星星:', star, city)
  
  // 获取关卡类型
  const levelType = getLevelTypeFromIcon(star.icon)
  if (!levelType) {
    showToast('无法识别关卡类型')
    return
  }
  
  // 获取第一个场景
  const scene = city.scenes[0]

  if (!scene) {
    showToast('城市场景配置错误')
    return
  }
  
  // 开始关卡
  await startLevel(city.id, scene.scene_id, levelType)
}

// 开始关卡
const startLevel = async (cityId, sceneId, levelType) => {
  try {
    const result = await citiesService.startLevel(cityId, sceneId, levelType)
    
    if (result.success) {
      if (result.requires_reset_confirmation) {
        // 需要重置确认
        showResetConfirmation({
          cityId,
          sceneId,
          levelType,
          message: result.reset_message
        })
      } else {
        // 直接开始游戏
        navigateToGame(result.level_config, cityId, sceneId, levelType)
      }
    } else {
      showFailToast(result.error || '开始关卡失败')
    }
  } catch (error) {
    console.error('❌ 开始关卡异常:', error)
    showFailToast('开始关卡失败')
  }
}

// 显示重置确认对话框
const showResetConfirmation = (resetAction) => {
  pendingResetAction.value = resetAction
  resetDialogMessage.value = resetAction.message
  showResetDialog.value = true
}

// 确认重置
const confirmReset = async () => {
  if (!pendingResetAction.value) return
  
  const { cityId, sceneId, levelType } = pendingResetAction.value
  
  try {
    const result = await citiesService.resetLevel(cityId, sceneId, levelType)
    
    if (result.success) {
      showSuccessToast(result.message || '关卡重置成功')
      
      // 重新加载城市数据
      await loadCities()
      
      // 获取关卡配置并开始游戏
      const levelConfigs = await citiesService.getLevelConfigs(cityId)
      if (levelConfigs.success) {
        const levelConfig = levelConfigs.level_types[levelType]
        navigateToGame(levelConfig, cityId, sceneId, levelType)
      } else {
        // 如果获取配置失败，使用默认配置
        navigateToGame(null, cityId, sceneId, levelType)
      }
    } else {
      showFailToast(result.error || '关卡重置失败')
    }
  } catch (error) {
    console.error('❌ 重置关卡异常:', error)
    showFailToast('重置关卡失败')
  } finally {
    showResetDialog.value = false
    pendingResetAction.value = null
  }
}

// 取消重置
const cancelReset = () => {
  showResetDialog.value = false
  pendingResetAction.value = null
}

// 导航到游戏页面
const navigateToGame = (levelConfig, cityId, sceneId, levelType) => {
  // 根据城市ID和关卡类型获取正确的场景文件路径
  const sceneFile = levelConfig?.scene_file || citiesService.getSceneFileByLevelType(levelType, cityId)

  console.log(`🎮 导航到游戏: 城市=${cityId}, 场景=${sceneFile}, 关卡类型=${levelType}`)

  router.push({
    path: '/game',
    query: {
      scene: sceneFile,
      cityId: cityId,
      sceneId: sceneId,
      levelType: levelType
    }
  })
}

// 获取城市的星级展示数据
const getCityStars = (city) => {
  const stars = []
  const levelTypes = ['thief', 'garbage', 'quiz']
  
  // 获取每个关卡类型的完成状态
  const levelStatus = {}
  const firstScene = city.scenes?.[0]

  if (firstScene?.levels) {
    if (Array.isArray(firstScene.levels)) {
      // levels是数组，可能包含关卡对象或字符串
      firstScene.levels.forEach(level => {
        if (typeof level === 'object') {
          // 关卡对象
          levelStatus[level.type] = level.is_completed
        } else {
          // 字符串，默认为未完成
          levelStatus[level] = false
        }
      })
    }
  }
  
  levelTypes.forEach(levelType => {
    stars.push({
      achieved: levelStatus[levelType] || false,
      icon: citiesService.getLevelTypeIcon(levelType),
      levelType: levelType,
      color: citiesService.getLevelTypeColor(levelType)
    })
  })
  
  return stars
}

// 根据图标获取关卡类型
const getLevelTypeFromIcon = (iconPath) => {
  if (iconPath.includes('thieficon-2.png')) {
    return 'thief'
  } else if (iconPath.includes('rubbish-1.png')) {
    return 'garbage'
  } else if (iconPath.includes('questions_icon.png')) {
    return 'quiz'
  }
  return null
}

// 获取城市解锁等级
const getCityUnlockLevel = (city) => {
  return city.unlock_level || 1
}
</script>

<style lang="scss" scoped>
// 定義顏色變數
$color-yellow: #fec107;
$color-purple-start: #a247e8;
$color-purple-end: #5434d2;
$color-blue-start: #32c5e8;
$color-blue-end: #2389c0;
$color-dark-bg: #1e1a3e;
$color-tag-bg: #8c92a5;

.game-level-page {
  background: linear-gradient(90deg, #6E6D6D 0%, #353535 100%);
  height: 100vh;
  padding: 90px 0;
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  color: white;
}

.ranking-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: url('/img/page_icons/cities_ranking_btn.png') no-repeat center center;
  background-size: 100% 100%;
  color: white;
  width: 228px;
  height: 88px;
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .rank-icon {
    height: 50px;
    margin-right: 10px;
  }

  .ranking-text {
    font-weight: bold;
    font-size: 24px;
  }
}

.level-list {
  padding-top: 50px; // 為排行榜按鈕留出空間
}

.level-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  margin-bottom: 40px;
  width: 692px;
  height: 228px;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &.locked {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
    }
  }

  // 應用不同的漸層主題
  &.theme-purple {
    background: url('/img/page_icons/cities_bg1.png') no-repeat center center;
    background-size: 100% 100%;
  }
  &.theme-blue {
     background: url('/img/page_icons/cities_bg2.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.unlock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: inherit;
  color: white;
  
  p {
    margin: 8px 0 0 0;
    font-size: 18px;
  }
}

.level-name-tag {
  z-index: 10;
  position: absolute;
  top: -20px; // 將標籤定位在卡片頂部之上
  left: 20px;
  width: 268px;
  height: 72px;
  background: url('/img/page_icons/cities_name_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.level-info {
  display: flex;
  align-items: center;
    transform: rotate(-16deg);
    margin-left: 20px;
    margin-top: 20px;
  .level-thumbnail {
    border-radius: 8px;
    overflow: hidden; // 確保圓角生效
  }

  .trophy-icon {
    color: #ffd700; // 金色
    margin-left: 20px;
  }
}

.level-stars {
  display: flex;
  gap: 20px; // 星星之間的間距
}

.star-slot {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 110px;
    border: 4px solid #fff;
    border-radius: 40px;
    padding: 10px 24px;
    gap: 8px;
  .star-background {
    width: 48px;
  }

  .inner-icon {
    height: 60px;
  }
}

</style>