<template>
  <div class="game-container">
    <!-- krpano全景图容器 -->
    <div id="krpano-container" :class="{ hidden: isUIHidden }"></div>
    
    <!-- 游戏UI界面 -->
    <div v-if="!isUIHidden" class="game-ui">
      <!-- 顶部状态栏 -->
      <div class="top-status-bar">

        <!-- 右侧按钮组 -->
        <div class="top-right-buttons">
          <div class="guardian-avatar-section" @click="showGuardianCard = true">
          <div class="wings-decoration">
            <img src="/img/page_icons/guard_level1.png" alt="Wings" class="wings-img" />
          </div>
          <div class="guardian-avatar">
            <img :src="gameStore.userInfo.avatar_url" alt="" class="avatar-img" />
          </div>
          <div class="level-badge">LV.{{ gameStore.userInfo?.guardian_level }}</div>
        </div>
          
        </div>

        <!-- 小偷图标和计数 -->
        <div class="status-item thief-remaining" v-if="starType === 'thief'">
            <img src="/img/page_icons/thieficon-2.png" class="thief-icon" />
            <span class="counter">{{ gameStore.hotspotCounts.remainingThieves }} / {{ gameStore.hotspotCounts.totalThieves }}</span>
        </div>

        <!-- 垃圾图标和计数 -->
        <div class="status-item garbage-remaining" v-if="starType === 'garbage'">
            <img src="/img/page_icons/rubbish-1.png" class="garbage-icon" />
            <span class="counter">{{ gameStore.hotspotCounts.remainingGarbage }} / {{ gameStore.hotspotCounts.totalGarbage }}</span>
        </div>

        
        <!-- 守望者等级 -->
        <div class="status-item guardian-level-display" @click="showGuardianCard = true" v-if="starType === 'monumentQuiz'">
            <img src="/img/page_icons/guardian_exp.png" class="guardian-icon" />
            <!-- {{ gameStore.getGuardianLevelName(gameStore.guardianLevel) }} -->
            <span class="guardian-level-text">{{ gameStore.civilizationExp }}</span>
        </div>
        
        
        
      </div>
      
      <!-- BOSS区域 -->
      <!-- <div class="boss-area">
        <div class="boss-container">
          <div class="boss-label">垃圾大王</div>
          <div class="boss-health-bar">
            <div class="health-bar-bg">
              <div 
                class="health-bar-fill" 
                :style="{ width: `${gameStore.bossHealth}%` }"
              ></div>
            </div>
            <div class="health-text">{{ gameStore.bossHealth }}%</div>
          </div>
        </div>
      </div> -->
      
      <!-- 左侧工具栏 -->
      <LeftToolbar
        @openTasks="showTasks = true"
        @openRanking="showRanking = true"
        @openCities="showCities = true"
      />
      
      <!-- 右侧道具栏 -->
      <RightToolbar
        @radar-scan-result="onRadarScanResult"
        @magnifier-result="onMagnifierResult"
        @open-lottery-wheel="showLotteryWheel = true"
      />
      
    </div>
    
 

    <!-- 弹窗组件 -->
    <GameMenu 
      @openUpgrade="showUpgrade = true" 
      @openRanking="showRanking = true"  
      @openTasks="showTasks = true" 
      @openCardView="showCardView = true" 
      @openCollectionStats="showCollectionStats = true"
    />
    <LotteryWheel v-model:show="showLotteryWheel" />
    <RankingList v-model:show="showRanking" />
    <PassiveIncome v-model:show="showPassiveIncome" @resource-updated="onResourceUpdated" />
    <TaskCenter
      v-model:show="showTasks"
      @task-completed="onTaskCompleted"
      @reward-claimed="onRewardClaimed"
    />
    <GameGuide v-model="showGuide" @complete="onGuideComplete" />
    <CityDialog v-model:show="showCities" :current-city-id="gameStore.currentCity" @city-selected="onCitySelected" @openMonumentQuiz="handleMonumentClick" />
    <CardView v-model:show="showCardView" />

    <!-- 新增的组件 -->
    <BossDialogue 
      v-model:show="showBossDialogue" 
      :stage="bossDialogueStage"
      :health-percentage="Math.round(gameStore.bossHealth)"
      @continue="onBossDialogueContinue"
      @reward-claimed="onBossRewardClaimed"
      @close="showBossDialogue = false"
    />
    
    <MonumentQuiz
      v-model:show="showMonumentQuiz"
      :monument-id="currentMonumentId"
      :quiz-type="currentQuizType"
      :difficulty="currentQuizDifficulty"
      :view-mode="currentQuizViewMode"
      @success="onMonumentSuccess"
      @failure="onMonumentFailure"
      @close="showMonumentQuiz = false"
    />
    
    <TreasureBoxDialog
      v-model:show="showTreasureBox"
      :treasure-box="currentTreasureBox"
      @claim="claimTreasureBox"
    />

    <!-- 守护者信息卡片 -->
    <van-popup 
      v-model:show="showGuardianCard" 
      position="center" 
      :style="{ background: 'transparent' }"
      closeable
      :close-icon="'/img/page_icons/close-btn.png'"
      close-icon-position="top-right"
      round
    >
      <GuardianCard />
    </van-popup>

    <!-- 经验系统卡片 -->
    <van-popup 
      v-model:show="showExperienceCard" 
      position="center" 
      :style="{ background: 'transparent' }"
      round
    >
      <ExperienceCard />
    </van-popup>

    <!-- 收集统计卡片 -->
    <van-popup 
      v-model:show="showCollectionStats" 
      position="center" 
      :style="{ background: 'transparent' }"
      round
    >
      <CollectionStatsCard />
    </van-popup>

    <AutoReward ref="autoRewardRef" />
    <GameActions
      :isUIHidden="isUIHidden"
      @toggleUI="handleToggleUI"
      @openSetup="showGameMenu = true"
    />
    <GameMenu v-model:show="showGameMenu"  />
  </div>
 
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGameStore } from '@/stores/game'
import { useAuthStore } from '@/stores/auth'
import krpanoManager from '@/utils/krpano'
import gameService from '@/services/gameService'
import dataService from '@/services/dataService'
import { api } from '@/utils/api'
import GameMenu from '@/components/game/GameMenu.vue'
import RankingList from '@/components/game/RankingList.vue'
import TaskCenter from '@/components/game/TaskCenter.vue'
import LeftToolbar from '@/components/game/LeftToolbar.vue'
import RightToolbar from '@/components/game/RightToolbar.vue'
import GameGuide from '@/components/game/GameGuide.vue'
import CityDialog from '@/components/game/CityDialog.vue'
import PassiveIncome from '@/components/game/PassiveIncome.vue'
import AutoReward from '@/components/game/AutoReward.vue'
import BossDialogue from '@/components/game/BossDialogue.vue'
import MonumentQuiz from '@/components/game/MonumentQuiz.vue'
import TreasureBoxDialog from '@/components/game/TreasureBoxDialog.vue'
import CardView from '@/components/game/CardView.vue'
import LotteryWheel from '@/components/game/LotteryWheel.vue'
import GuardianCard from '@/components/game/GuardianCard.vue'
import ExperienceCard from '@/components/game/ExperienceCard.vue'
import CollectionStatsCard from '@/components/game/CollectionStatsCard.vue'
import GameActions from '@/components/game/GameActions.vue'
import { showToast, showSuccessToast, showFailToast, showLoadingToast } from 'vant'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import audioManager from '@/utils/audio'
import { useRoute } from 'vue-router'

// 导入热点服务
import krpanoHotspotManager from '@/services/krpanoHotspotManager'

const gameStore = useGameStore()
const authStore = useAuthStore()
const route = useRoute()

// UI状态
const isUIHidden = ref(false)
const showGameMenu = ref(false)
const showUpgrade = ref(false)
const showRanking = ref(false)
const showCollection = ref(false)
const showCardView = ref(false)
const showTasks = ref(false)
const showGuide = ref(false)
const showCities = ref(false)
const showPassiveIncome = ref(false)
const showBossDialogue = ref(false)
const showMonumentQuiz = ref(false)
const showTreasureBox = ref(false)
const showLotteryWheel = ref(false)
const showGuardianCard = ref(false)
const showExperienceCard = ref(false)
const showCollectionStats = ref(false)
const currentTreasureBox = ref(null)
const bossDialogueStage = ref(1)
const currentMonumentId = ref('monument_1')
const currentQuizType = ref('monument')  // 'monument' 或 'artifact'
const currentQuizDifficulty = ref('medium')
const currentQuizViewMode = ref(false)  // 是否为查看模式


// 添加自动奖励组件引用
const autoRewardRef = ref(null)

// 加载用户资源
const loadUserResources = async () => {
  try {
    const response = await api.user.getProfile()
    if (response.data) {
      const user = response.data

      // 同步到新的文明经验值系统
      gameStore.syncResources(
        user.guardian_exp || 0,
        user.stamina || 100
      )

      // 同步收集数据
      gameStore.updateSessionData({
        thievesCollected: user.thieves_collected || 0,
        garbageCollected: user.garbage_collected || 0,
        artifactsCollected: 0, // 这个字段可能需要从其他API获取
        monumentsProtected: user.total_monuments_restored || 0,
        civilizationExp: user.guardian_exp || 0,
        stamina: user.stamina || 100
      })

      // console.log('用户资源加载成功:', {
      //   guardianLevel: user.guardian_level,
      //   guardianExp: user.guardian_exp,
      //   stamina: user.stamina,
      //   thieves: user.thieves_collected,
      //   garbage: user.garbage_collected,
      //   monuments: user.total_monuments_restored
      // })

    } else {
      // console.error('获取用户资源失败: 响应数据为空')
    }
  } catch (error) {
    // console.error('加载用户资源异常:', error)
    // 使用默认值
    gameStore.syncResources(0, 100)
    gameStore.updateSessionData({
      thievesCollected: 0,
      garbageCollected: 0,
      artifactsCollected: 0,
      monumentsProtected: 0,
      civilizationExp: 0,
      stamina: 100
    })
  }
}

// 隐藏/显示UI
const hideUI = (value) => {
  isUIHidden.value = value
  // 隐藏krpano热点
  const hotspots = krpanoManager.get('hotspot')

  hotspots.forEach((_, hotspotId) => {
    krpanoManager.call(`set(hotspot[${hotspotId}].visible, false)`)
  })

}

const showUI = () => {
  isUIHidden.value = false
}

// 处理GameActions组件的UI切换事件
const handleToggleUI = (shouldHide) => {
  // console.log('🎮 GameActions触发UI切换:', shouldHide)
  hideUI(shouldHide)
}

const adsCallbacks = {
  adFinished: () => console.log("End midgame ad"),
  adError: (error) => console.log("Error midgame ad", error),
  adStarted: () => console.log("Start midgame ad"),
};
// const showAds = () => {
//   console.log('showAds')
  
// // window.CrazyGames.SDK.ad.requestAd("midgame", adsCallbacks);
// // or
// window.CrazyGames.SDK.ad.requestAd("rewarded", adsCallbacks);
// }

// 加载特定类型的场景
const loadSpecificScene = async (sceneFile, starType) => {
  try {
    // console.log(`🎯 正在加载特定场景: ${sceneFile}, 类型: ${starType}`)

    await krpanoManager.init('krpano-container', sceneFile)

    // 根据关卡类型执行相应的初始化
    switch(starType) {
      case 'thief':
        // console.log('🕵️ 初始化小偷挑战场景')
        showToast('小偷挑战场景已加载！开始抓捕小偷吧！')
        break

      case 'garbage':
        // console.log('🗑️ 初始化垃圾清理场景')
        showToast('垃圾清理场景已加载！开始清理垃圾吧！')
        break

      case 'quiz':
      case 'monumentQuiz':
        // console.log('🏛️ 初始化古迹问答场景')
        showToast('古迹问答场景已加载！开始探索古迹吧！')
        break

      default:
        // console.log('❓ 未知场景类型:', starType)
        showToast(`场景已加载: ${starType}`)
    }

    // console.log(`✅ 特定场景 ${sceneFile} 加载完成`)

  } catch (error) {
    // console.error('❌ 加载特定场景失败:', error)
    showFailToast('场景加载失败，请检查场景文件')
    // 不回退到默认场景，让用户知道具体问题
  }
}
const targetScene = route.query.scene
const starType = route.query.levelType
// 初始化游戏
const initGame = async () => {
  try {
    // console.log('开始初始化游戏...')
    
    authStore.loadFromStorage()
    
    // 1. 检查并加载持久化的游戏状态
    const hasGameState = gameStore.loadGameState()
    if (hasGameState) {
      // console.log('已加载持久化的游戏状态')
    }
    
    // console.log(authStore.isLoggedIn,'----')
    // 2. 检查用户登录状态
    if (authStore.isLoggedIn) {
      // 用户已登录，刷新用户信息
      // console.log('用户已登录，刷新用户信息')
      await gameService.refreshUserInfo()
    } else if (!authStore.isLoggedIn) {
      // 未登录用户，初始化游客模式
      gameService.initOfflineMode()
      showToast('欢迎游客用户！以离线模式游戏')
    }

    // 3. 加载本地收集数据
    gameStore.loadCollections()

    // 4. 初始化krpano
    
    
    if (targetScene && starType) {
      // console.log('从路由参数加载特定场景:', targetScene, '类型:', starType)
      await loadSpecificScene(`/scenes/${targetScene}`, starType)
    } else {
      // 默认加载第一个场景
      // krpanoManager.loadScene('scene_level_1')
      // await krpanoManager.init('krpano-container', '/tour.xml')
    }

    // 6. 预初始化热点管理系统
    try {
      await krpanoHotspotManager.initialize('scene_level_1')
      // console.log('krpano热点管理系统预初始化完成')
    } catch (error) {
      console.error('krpano热点管理系统初始化失败:', error)
    }
    
    // 7. 注册krpano事件
    setupKrpanoEvents()

    // 8. 等待场景完全加载
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 9. 开始游戏会话（获取收集状态和过滤热点数据）
    const cityId = route.query.cityId || 'beijing'
    const sceneId = route.query.sceneId || 'scene_level_1'
    // console.log(`🎮 开始游戏会话: 城市=${cityId}, 场景=${sceneId}`)
    const sessionResult = await gameService.startGameSession(cityId, sceneId)
    
    // 10. 基于会话结果初始化热点系统
    if (sessionResult.success) {
      // console.log('✅ 游戏会话启动成功，开始初始化热点系统')
      
      // 10.1. 更新热点数量统计
      await updateHotspotCounts()
      // console.log('✅ 热点数量统计完成:', gameStore.hotspotCounts)
      
      // 10.2. 设置已收集热点到Worker
      if (gameStore.collectedHotspotIds && gameStore.collectedHotspotIds.length > 0) {
        // console.log(`🎯 设置已收集热点到Worker: ${gameStore.collectedHotspotIds.length} 个`)
        // 通过Worker管理收集状态，Worker会在显示逻辑中自动过滤已收集的热点
        if (window.enhancedGameWorker) {
          // 将响应式数组转换为普通数组，避免Worker克隆错误
          const collectedIds = [...gameStore.collectedHotspotIds]
          window.enhancedGameWorker.postMessage({
            type: 'setCollectedHotspots',
            data: collectedIds
          })
        }
      }

      // 10.3. 初始化增强热点管理器（现在有了收集状态数据）
      setTimeout(() => {
        initializeEnhancedWorker()
      }, 500)
      
      // 10.4. 显示游戏开始提示
      if (sessionResult.session.isOffline) {
        const isCrazyGames = !!(window.CrazyGames && window.CrazyGames.SDK)
        if (!isCrazyGames) {
          showToast('离线模式游戏开始！')
        }
      } else {
        const isCrazyGames = !!(window.CrazyGames && window.CrazyGames.SDK)
        if (!isCrazyGames) {
          showToast('在线模式游戏开始！')
        }
      }
    } else {
      // 即使会话启动失败，也要确保游戏能够进行
      gameStore.startGame()
      const isCrazyGames = !!(window.CrazyGames && window.CrazyGames.SDK)
      if (!isCrazyGames) {
        showToast('游戏会话启动失败，但仍可游戏')
      }
    }

    // 8. 暴露函数给krpano使用
    window.showToast = showToast


    // 10. 检查是否需要显示引导
    checkAndShowGuide()

    // 11. 播放背景音乐
    audioManager.playBgMusic('/audios/Monoman - Meditation Monoman.ogg')

    // console.log('游戏初始化完成！')


  } catch (error) {
    console.error('初始化游戏失败:', error)
    showToast('游戏初始化失败: ' + error.message)
  }
}

// 初始化增强Worker系统
const initializeEnhancedWorker = () => {
  if (window.enhancedGameWorker) return

  // console.log('🚀 初始化增强Worker系统...')
  
  // 创建Worker
  window.enhancedGameWorker = new Worker('/enhanced-game-worker.js')

  // 设置消息处理
  window.enhancedGameWorker.onmessage = function(e) {
    const { type, ...data } = e.data

    switch (type) {
      case 'hotspotsInitialized':
        // console.log(`✅ Worker热点初始化完成: ${data.count} 个热点`)
        break

      case 'hotspotUpdates':
        console.log('🔄 收到Worker热点更新:', data.updates.length, '个更新')
        if (data.updates && data.updates.length > 0) {
          applyEnhancedHotspotUpdates(data.updates)
        }
        break

      case 'clickAnimation':
        executeClickAnimation(data.animation)
        break

      case 'imagePreloaded':
        console.log(`📷 图片预加载: ${data.url} (${data.cacheSize}/${data.cacheSize + data.remaining})`)
        break

      case 'preloadComplete':
        console.log(`✅ Worker图片预加载完成: ${data.totalImages} 张图片`)
        showToast(`图片预加载完成 (${data.totalImages}张)`)
        break

      case 'animationComplete':
        onAnimationComplete(data.hotspotName, data.animationType)
        break

      case 'collectedHotspotsSet':
        console.log(`✅ Worker已设置收集状态: ${data.count} 个热点`)
        break

      case 'hotspotMarkedCollected':
        console.log(`✅ Worker已标记热点收集: ${data.hotspotId}`)
        break

      case 'error':
        // console.error('❌ Worker错误:', data.message)
        showToast('Worker发生错误')
        break

      default:
        console.warn(`⚠️ 未处理的Worker消息类型: ${type}`, data)
        break
    }
  }

  // console.log('✅ 增强Worker系统初始化完成')
  
  // 初始化热点数据到Worker
  setTimeout(() => {
    initializeHotspotsInWorker()
  }, 1000)
  
  // 设置视角变化监听
  setupViewChangeListener()
}

// 初始化热点数据到Worker - 根据收集状态过滤
const initializeHotspotsInWorker = async () => {
  if (!window.enhancedGameWorker || !krpanoManager.isReady()) return

  try {
    // 从游戏商店获取已过滤的热点数据（已排除收集的热点）
    const gameStore = useGameStore()
    let allHotspots = []
    
    // 如果游戏商店中有过滤后的热点数据，使用它们
    if (gameStore.thieves && gameStore.garbages && gameStore.treasures) {
      allHotspots = [
        ...gameStore.thieves.map(h => ({ ...h, type: 'thief', baseScale: 1.0 })),
        ...gameStore.garbages.map(h => ({ ...h, type: 'garbage', baseScale: 1.0 })),
        ...gameStore.treasures.map(h => ({ ...h, type: 'treasure', baseScale: 1.0 })),
        ...(gameStore.quizHotspots || []).map(h => ({ ...h, type: 'quiz', baseScale: 1.0 }))
      ]

      // 发送过滤后的热点数据到Worker
      // 确保数据可序列化，移除可能的响应式属性
      const serializableHotspots = JSON.parse(JSON.stringify(allHotspots))
      window.enhancedGameWorker.postMessage({
        type: 'initializeHotspots',
        data: serializableHotspots
      })

      // console.log(`🎯 发送已过滤的热点数据到Worker: ${allHotspots.length} 个热点（已排除收集的热点）`)
      
      
    } else {
      // 如果没有过滤数据，先获取原始数据
      // console.log('⚠️ 游戏商店中没有过滤后的热点数据，获取原始数据')
      const hotspotsFromKrpano = await gameService.getHotspotsFromKrpano()
      allHotspots = [
        ...hotspotsFromKrpano.thieves.map(h => ({ ...h, type: 'thief', baseScale: 1.0 })),
        ...hotspotsFromKrpano.garbage.map(h => ({ ...h, type: 'garbage', baseScale: 1.0 })),
        ...hotspotsFromKrpano.treasure.map(h => ({ ...h, type: 'treasure', baseScale: 1.0 })),
        ...hotspotsFromKrpano.quiz.map(h => ({ ...h, type: 'quiz', baseScale: 1.0 }))
      ]

      // 确保数据可序列化，移除可能的响应式属性
      const serializableHotspots = JSON.parse(JSON.stringify(allHotspots))
      window.enhancedGameWorker.postMessage({
        type: 'initializeHotspots',
        data: serializableHotspots
      })

      // console.log(`📤 发送原始热点数据到Worker: ${allHotspots.length} 个热点`)
    }

    // 设置已收集的热点状态到Worker
    if (gameStore.collectedHotspotIds && gameStore.collectedHotspotIds.length > 0) {
      setTimeout(() => {
        // 将响应式数组转换为普通数组，避免Worker克隆错误
        const collectedIds = [...gameStore.collectedHotspotIds]
        window.enhancedGameWorker.postMessage({
          type: 'setCollectedHotspots',
          data: collectedIds
        })
        // console.log(`🎯 设置已收集热点状态到Worker: ${collectedIds.length} 个`)
      }, 100) // 稍微延迟，确保热点初始化完成
    }

    // 延迟调用XML的init_hotspot_lod_state，传递实际的热点数据
    setTimeout(() => {
      if (krpanoManager.isReady() && allHotspots.length > 0) {
        // console.log(`🎯 调用XML init_hotspot_lod_state，传递 ${allHotspots.length} 个热点数据`)
        
        // 将热点数据转换为XML可接受的格式
        const hotspotNames = allHotspots.map(h => ({ name: h.name }))
        
        // 设置全局热点数据变量，供XML的hotspot_distance_manager使用
        krpanoManager.set('global_hotspot_data', hotspotNames)
        
        // 调用XML action，传递热点数据
        krpanoManager.call('init_hotspot_lod_state')
        krpanoManager.call('hotspot_distance_manager')
        // console.log(`✅ 已设置全局热点数据: ${hotspotNames.length} 个热点`)
      }
    }, 200) // 延迟200ms，确保Worker初始化完成

  } catch (error) {
    console.error('初始化热点数据到Worker失败:', error)
  }
}

// 设置视角变化监听
const setupViewChangeListener = () => {
  let lastView = { h: 0, v: 0, fov: 90 }
  
  // 定时器备用机制
  setInterval(() => {
    if (!krpanoManager.isReady() || !window.enhancedGameWorker) return

    const currentView = {
      h: krpanoManager.get('view.hlookat') || 0,
      v: krpanoManager.get('view.vlookat') || 0,
      fov: krpanoManager.get('view.fov') || 90
    }

    const hDiff = Math.abs(currentView.h - lastView.h)
    const vDiff = Math.abs(currentView.v - lastView.v)
    const fovDiff = Math.abs(currentView.fov - lastView.fov)

    if (hDiff > 1 || vDiff > 1 || fovDiff > 1) {
      // console.log('🔍 视角变化:', { h: currentView.h.toFixed(1), v: currentView.v.toFixed(1), fov: currentView.fov.toFixed(1) })
      
      // 发送视角更新到Worker
      window.enhancedGameWorker.postMessage({
        type: 'updateView',
        data: {
          hlookat: currentView.h,
          vlookat: currentView.v,
          fov: currentView.fov
        }
      })
      lastView = { ...currentView }
    }
  }, 100)

  // console.log('✅ 视角变化监听已设置')
}

// 应用增强热点更新
const applyEnhancedHotspotUpdates = (updates) => {
  if (!krpanoManager.isReady()) return

  // console.log('🎯 应用热点更新:', updates.length, '个更新')

  updates.forEach(update => {
    const { name, shouldShow, shouldHide, shouldUpdateLOD, showAnimation, hideAnimation, scale } = update
    
    // console.log(`热点 ${name}:`, { shouldShow, shouldHide, shouldUpdateLOD, scale })

    if (shouldShow && showAnimation) {
      // 执行显示动画
      // console.log(`👁️ 显示热点: ${name}`)
      krpanoManager.call(`
        set(hotspot[${name}].visible, true);
        set(hotspot[${name}].onclick, js(onThiefClicked(get(name))));
        set(hotspot[${name}].enabled, true);
        set(hotspot[${name}].alpha, 0);
        set(hotspot[${name}].scale, ${scale * 0.3});
        tween(hotspot[${name}].alpha, 1.0, ${showAnimation.duration / 1000}, easeOutBack);
        tween(hotspot[${name}].scale, ${scale}, ${showAnimation.duration / 1000 * 0.6}, easeOutBack);
      `)
    } else if (shouldHide && hideAnimation) {
      // 执行隐藏动画
      // console.log(`🙈 隐藏热点: ${name}`)
      krpanoManager.call(`
        set(hotspot[${name}].visible, false);
        set(hotspot[${name}].enabled, false);
      `)
    } else if (shouldUpdateLOD) {
      // 更新LOD
      // console.log(`🔍 更新LOD: ${name}, scale: ${scale}`)
      krpanoManager.call(`
        tween(hotspot[${name}].scale, ${scale}, 0.3, easeOutQuart);
      `)
    }
  })
}

// 执行点击动画
const executeClickAnimation = (animation) => {
  if (!krpanoManager.isReady()) return

  const { name, phases } = animation
  let animationCode = `set(hotspot[${name}].enabled, false);`

  phases.forEach(phase => {
    const delay = phase.delay || 0
    const duration = phase.duration / 1000

    if (phase.keyframes) {
      // 关键帧动画
      phase.keyframes.forEach((value, index) => {
        const time = (duration / (phase.keyframes.length - 1)) * index
        animationCode += `
          delayedcall(${(delay + time * 1000) / 1000},
            set(hotspot[${name}].${phase.property}, ${value});
          );
        `
      })
    } else {
      // 简单补间动画
      animationCode += `
        delayedcall(${delay / 1000},
          tween(hotspot[${name}].${phase.property}, ${phase.to}, ${duration}, ${phase.easing});
        );
      `
    }
  })

  // 最终隐藏
  animationCode += `
    delayedcall(${animation.duration / 1000},
      set(hotspot[${name}].visible, false);
    );
  `

  krpanoManager.call(animationCode)
}

// 动画完成回调
const onAnimationComplete = (hotspotName, animationType) => {
  // console.log(`动画完成: ${hotspotName} (${animationType})`)
}

// 触发点击动画
const triggerClickAnimation = (hotspotName, type) => {
  if (!window.enhancedGameWorker) return

  // 发送动画请求到Worker
  window.enhancedGameWorker.postMessage({
    type: 'triggerClickAnimation',
    data: {
      hotspotName,
      animationType: type
    }
  })
}

// 设置krpano事件监听
const setupKrpanoEvents = () => {
  // 小偷点击事件
  krpanoManager.on('thiefClicked', (name) => {
    handleThiefClick(name)
  })
  
  // 垃圾点击事件
  krpanoManager.on('garbageClicked', (name) => {
    handleGarbageClick(name)
  })
  
  // 宝箱点击事件
  krpanoManager.on('treasureClicked', (name) => {
    handleTreasureClick(name)
  })
  
  // 文明古迹点击事件
  krpanoManager.on('monumentClicked', (name) => {
    handleMonumentClick(name)
  })

  // 场景切换事件
  krpanoManager.on('sceneTransition', () => {
    // console.log('场景切换事件')
  })
  

 
}

// 通用的奖励处理函数
const showRewardAnimation = (reward) => {
  if (reward.type === 'treasure_box') {
    // 宝箱奖励
    const contents = reward.contents || {}
    autoRewardRef.value?.show({
      type: 'treasure_box',
      contents: contents
    })
  } else {
    // 单一奖励 - 将所有奖励转换为文明经验值系统
    let rewardType = 'civilization_exp'
    let amount = reward.amount || 10
    
    // 根据奖励类型调整
    if (reward.type === 'civilization_exp') {
      rewardType = 'civilization_exp'
    } else if (reward.type === 'stamina') {
      rewardType = 'stamina'
      amount = (reward.amount || 1) * 5
    }
    
    autoRewardRef.value?.show({
      type: rewardType,
      amount: amount
    })
  }
}

// 防重复调用的集合
const processingHotspots = new Set()

/**
 * 统一的热点点击处理函数
 * 支持所有类型热点：小偷、垃圾、宝箱、古迹
 */
const handleHotspotClick = async (name, type) => {
  console.log(`🎯 热点点击: ${name} (类型: ${type})`)

  // 防重复调用
  if (processingHotspots.has(name)) {
    console.log('⚠️ 热点正在处理中，忽略重复调用:', name)
    return
  }

  processingHotspots.add(name)

  try {
    // 触发点击动画
    triggerClickAnimation(name, type)
    
    // 收集热点
    const result = await gameService.collectHotspot(name)
    
    if (result.success) {
      console.log('✅ 热点收集成功:', result)

      // 通知Worker标记热点为已收集，Worker会自动处理隐藏逻辑
      if (window.enhancedGameWorker) {
        console.log('📤 通知Worker隐藏热点:', name)
        window.enhancedGameWorker.postMessage({
          type: 'markHotspotCollected',
          data: { hotspotId: name }
        })
      }

      // 更新顶部界面的计数显示
      gameStore.collectHotspotByType(type)

      // 显示奖励动画
      if (result.result.rewards) {
        showRewardAnimation({
          type: result.result.rewards.type,
          amount: result.result.rewards.experience
        })
      }
      
      // 检查是否触发文化问答
      if (result.result.artifact_drop) {
        const {artifact_id} = result.result.artifact_drop
        if (artifact_id) {
          showCulturalQuizForCollection(
            artifact_id, 
            'medium'
          )
        }
      }
      
      // 检查关卡完成状态
      checkLevelComplete()
    } else {
      console.error('热点收集失败:', result.error)
    }
    
  } catch (error) {
    console.error('处理热点点击失败:', error)
  } finally {
    // 清理处理状态
    processingHotspots.delete(name)
  }
}

// 兼容旧的事件处理函数
const handleThiefClick = (name) => handleHotspotClick(name, 'thief')

const handleGarbageClick = (name) => handleHotspotClick(name, 'garbage')

const handleTreasureClick = (name) => handleHotspotClick(name, 'treasure')

// 处理文明古迹点击
const handleMonumentClick = (data) => {
  // 处理CityDialog传来的图鉴信息
  if (typeof data === 'object') {
    // 新的数据格式：包含图鉴详细信息
    currentMonumentId.value = data.artifactId
    currentQuizType.value = 'artifact'
    currentQuizViewMode.value = data.viewMode // 是否为查看模式
    // console.log(`打开图鉴问答: ${data.artifactName}, 查看模式: ${data.viewMode}`)
  } else {
    // 兼容旧的字符串格式
    currentMonumentId.value = data
    currentQuizType.value = 'monument'
    currentQuizViewMode.value = false
  }
  
  showMonumentQuiz.value = true
}


// BOSS对话事件处理
const onBossDialogueContinue = () => {
  // 继续游戏
  gameStore.resumeGame()
  showBossDialogue.value = false
}

const onBossRewardClaimed = (reward) => {
  // 处理BOSS对话奖励
  if (reward.civilizationExp) {
    gameStore.addCivilizationExp(reward.civilizationExp)
  }
  if (reward.stamina) {
    gameStore.restoreStamina(reward.stamina)
  }
}

// 古迹问答事件处理
const onMonumentSuccess = (reward) => {
  // 古迹问答成功
  gameStore.protectMonument(true)
  if (reward.civilizationExp) {
    gameStore.addCivilizationExp(reward.civilizationExp)
  }
  showSuccessToast('古迹保护成功！')
}

const onMonumentFailure = () => {
  // 古迹问答失败
  gameStore.protectMonument(false)
  showFailToast('古迹保护失败！')
}




// 显示收集触发的文化图鉴问答（使用artifacts接口）
const showCulturalQuizForCollection = (artifactId, difficulty = 'medium') => {
  // console.log('触发收集文化图鉴问答:', artifactId, difficulty)
  // 设置为文化图鉴模式，而不是古迹模式
  currentMonumentId.value = artifactId
  currentQuizType.value = 'artifact'  // 标识这是文化图鉴问答
  currentQuizDifficulty.value = difficulty
  showMonumentQuiz.value = true
}


const claimTreasureBox = (useAd = false) => {
  const treasure = currentTreasureBox.value
  if (!treasure) return
  
  const multiplier = useAd ? 2 : 1
  
  switch (treasure.type) {
    case 'civilization_exp':
      gameStore.addCivilizationExp(treasure.amount * multiplier)
      showSuccessToast(`获得 ${treasure.amount * multiplier} 文明经验值！`)
      break
      
    case 'stamina':
      gameStore.restoreStamina(treasure.amount * multiplier)
      showSuccessToast(`恢复 ${treasure.amount * multiplier} 体力值！`)
      break
      
    case 'cultural_atlas':
      const atlasCount = treasure.amount * multiplier
      for (let i = 0; i < atlasCount; i++) {
        const atlas = {
          id: `atlas_${Date.now()}_${i}`,
          name: '珍贵文化图鉴',
          description: '从宝箱中获得的文化遗产记录'
        }
        gameStore.addCulturalAtlas(atlas)
      }
      showSuccessToast(`获得 ${atlasCount} 个文化图鉴！`)
      break
      
    case 'tools':
      if (treasure.tools) {
        treasure.tools.forEach(tool => {
          gameStore.tools[tool].count += multiplier
        })
        showSuccessToast(`获得道具奖励！`)
      }
      break
      
    case 'repentance_letter':
      // 悔过书给予额外文明经验值
      const expBonus = 30 * multiplier
      gameStore.addCivilizationExp(expBonus)
      showSuccessToast(`获得悔过书！额外获得 ${expBonus} 文明经验值！`)
      break
      
    case 'avatar_position':
      // 头像位置编辑权限（这里可以存储到用户偏好中）
      showSuccessToast(`获得头像位置编辑权限！可以自定义守望者头像位置`)
      // TODO: 实现头像位置编辑功能
      break
  }
  
  showTreasureBox.value = false
  currentTreasureBox.value = null
}








// 检查关卡是否完成
const checkLevelComplete = () => {
  
  // 如果所有小偷都被抓到，给予额外奖励
  if (gameStore.hotspotCounts.remainingThieves === 0) {
    const stars = calculateStars()
    gameStore.updateLevelStars(stars)
    
    showToast('关卡完成！所有小偷都被抓获！')
  }
}

// 计算星级
const calculateStars = () => {
  // 根据时间、收集情况等计算星级
  if (gameStore.gameTime > 240) return 3
  if (gameStore.gameTime > 180) return 2
  return 1
}

// 检查并显示引导
const checkAndShowGuide = () => {
  // 检查数据服务中是否已经显示过引导
  const hasShownGuide = dataService.getItem('gameGuideShown')
  
  if (!hasShownGuide) {
    // 延迟一秒后显示引导，让游戏先加载完成
    setTimeout(() => {
      showGuide.value = true
    }, 1000)
  }
}

// 引导完成处理
const onGuideComplete = () => {
  // console.log('游戏引导已完成')
  // 可以在这里添加其他逻辑，比如给新手奖励等
}




// 定时器
let gameTimer = null

const startGameTimer = () => {
  gameTimer = setInterval(() => {
    if (!gameStore.isGamePaused && gameStore.gameTime > 0) {
      gameStore.gameTime--
      
      // 道具冷却时间
      if (gameStore.tools.radar.cooldown > 0) {
        gameStore.tools.radar.cooldown--
      }
      if (gameStore.tools.magnifier.cooldown > 0) {
        gameStore.tools.magnifier.cooldown--
      }
      
      // 时间到了
      if (gameStore.gameTime === 0) {
        // TODO: 游戏结束逻辑
      }
    }
  }, 1000)
}


// 任务相关事件处理
const onTaskCompleted = (data) => {
  showSuccessToast(`恭喜完成任务：${data.taskName}`)
}

const onRewardClaimed = (data) => {
  // 更新用户资源 - 使用新的文明经验值系统
  if (data.rewards?.civilizationExp) {
    gameStore.addCivilizationExp(data.rewards.civilizationExp)
  }
  if (data.rewards?.stamina) {
    gameStore.restoreStamina(data.rewards.stamina)
  }
}

// 被动收益资源更新处理
const onResourceUpdated = (data) => {
  if (data.civilizationExp !== undefined) {
    gameStore.addCivilizationExp(data.civilizationExp)
  }
  if (data.stamina !== undefined) {
    gameStore.restoreStamina(data.stamina)
  }

  const rewardText = []
  if (data.rewards?.civilizationExp) rewardText.push(`${data.rewards.civilizationExp} 文明经验值`)
  if (data.rewards?.stamina) rewardText.push(`${data.rewards.stamina} 体力值`)

  showSuccessToast(`获得 ${rewardText.join(', ')}${data.isDouble ? ' (双倍奖励)' : ''}`)
}

// 雷达扫描结果处理
const onRadarScanResult = (data) => {
  // console.log('雷达扫描结果:', data)
  // 这里可以在地图上显示热点标记
}

// 放大镜结果处理
const onMagnifierResult = (data) => {
  // console.log('放大镜搜索结果:', data)
  showSuccessToast(`发现 ${data.foundItems.length} 个隐藏物品`)
}

// 城市选择处理
const onCitySelected = (cityId) => {
  // console.log('选择城市:', cityId)
  // 可以在这里切换到对应的城市场景
  if (cityId !== gameStore.currentCity) {
    gameStore.currentCity = cityId
    // showSuccessToast(`切换到 ${cityId}`)

    // 如果需要，可以加载对应城市的场景
    // krpanoManager.loadScene(`${cityId}_scene1`)
  }
}




// 更新热点数量统计（不覆盖已过滤的热点数据）
const updateHotspotCounts = async () => {
  try {
    // 检查gameStore是否已经有过滤后的热点数据
    if (gameStore.thieves.length > 0 || gameStore.garbages.length > 0 || gameStore.treasures.length > 0) {
      // console.log('🎯 使用已过滤的热点数据更新统计')
      // 使用gameStore中已过滤的热点数据更新统计
      const hotspotCounts = {
        totalThieves: gameStore.thieves.length + (gameStore.collectedHotspotIds?.filter(id => id.includes('thief')).length || 0),
        totalGarbage: gameStore.garbages.length + (gameStore.collectedHotspotIds?.filter(id => id.includes('garbage')).length || 0),
        totalTreasure: gameStore.treasures.length + (gameStore.collectedHotspotIds?.filter(id => id.includes('treasure')).length || 0),
        totalBoss: 0, // Boss不参与收集统计
        totalQuiz: gameStore.quizHotspots?.length || 0,
        remainingThieves: gameStore.thieves.length,
        remainingGarbage: gameStore.garbages.length,
        remainingTreasure: gameStore.treasures.length,
        remainingBoss: 0
      }
      
      gameStore.updateHotspotCounts(hotspotCounts)
      // console.log('✅ 基于过滤数据的热点统计:', hotspotCounts)
    } else {
      // console.log('⚠️ 没有过滤数据，从XML获取原始热点数据')
      // 如果没有过滤数据，从XML获取原始数据（仅用于统计）
      const hotspotsFromXML = await gameService.getHotspotsFromKrpano()

      // 更新统计数据，但不覆盖gameStore的热点数组
      const hotspotCounts = {
        totalThieves: hotspotsFromXML.thieves.length,
        totalGarbage: hotspotsFromXML.garbage.length,
        totalTreasure: hotspotsFromXML.treasure.length,
        totalBoss: hotspotsFromXML.boss.length,
        totalQuiz: hotspotsFromXML.quiz.length,
        remainingThieves: hotspotsFromXML.thieves.length,
        remainingGarbage: hotspotsFromXML.garbage.length,
        remainingTreasure: hotspotsFromXML.treasure.length,
        remainingBoss: hotspotsFromXML.boss.length
      }

      gameStore.updateHotspotCounts(hotspotCounts)
      // console.log('✅ 基于XML数据的热点统计:', hotspotCounts)
    }
  } catch (error) {
    console.error('更新热点数量失败:', error)
  }
}





onMounted(() => {
  initGame()
  startGameTimer()

  // 加载用户资源
  loadUserResources()

  // 监听页面卸载事件，保存游戏状态
  window.addEventListener('beforeunload', handleBeforeUnload)
  
  // 监听BOSS对话事件
  window.addEventListener('bossDialogue', (event) => {
    const { stage, healthPercent } = event.detail
    bossDialogueStage.value = stage
    showBossDialogue.value = true
    // console.log(`BOSS对话触发: 阶段${stage}, 血量${healthPercent}%`)
  })
  
  // 监听BOSS被击败事件
  window.addEventListener('bossDefeated', () => {
    showSuccessToast('恭喜！垃圾大王被击败了！')
    // 可以在这里添加胜利动画或跳转到下一关
  })
})

onUnmounted(async () => {
  if (gameTimer) {
    clearInterval(gameTimer)
  }
  
  // 清理krpano热点管理器
  try {
    await krpanoHotspotManager.cleanup()
  } catch (error) {
    console.error('清理krpano热点管理器失败:', error)
  }
  
  // 清理Worker
  if (window.enhancedGameWorker) {
    window.enhancedGameWorker.postMessage({ type: 'cleanup' })
    window.enhancedGameWorker.terminate()
    delete window.enhancedGameWorker
    // console.log('✅ 增强Worker已清理')
  }
  
  // 保存游戏状态
  gameStore.saveGameState()
  
  // 移除事件监听
  window.removeEventListener('beforeunload', handleBeforeUnload)
  window.removeEventListener('bossDialogue', () => {})
  window.removeEventListener('bossDefeated', () => {})
})

// 处理页面卸载
const handleBeforeUnload = () => {
  // 保存游戏状态
  gameStore.saveGameState()
  // console.log('页面即将卸载，游戏状态已保存')
}

</script>

<style lang="scss" scoped>
.game-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #000;
}

#krpano-container {
  width: 100%;
  height: 100%;
  
  // &.hidden {
  //   filter: blur(5px);
  // }
}

.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
  
  > * {
    pointer-events: auto;
  }
}

// 顶部状态栏
.top-status-bar {
  position: absolute;
  top: 20px;
  width: 100%;
  padding-left: 20px;
  padding-right: 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 28px;
  z-index: 100;
}

.status-item {
  width: 160px;
  height: 52px;
  background-image: url('/img/page_icons/UI-Counter-BG.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  align-items: center;
  
}
.thief-remaining, .garbage-remaining, .treasure-remaining {
    
    .thief-icon{
      width: 70px;
      height: 68px;
      margin-left: -30px;
    }

    .garbage-icon {
      width: 60px;
      height: 58px;
      margin-left: -40px;
    }

    .treasure-icon {
      width: 50px;
      height: 50px;
      margin-left: -40px;
    }
    
    .counter {
      font-size: 20px;
      color: #fff;
      font-weight: bold;
    }
}
// 新系统显示样式
.civilization-exp-display, .stamina-display, .guardian-level-display {
    
    // .civilization-exp-icon, .stamina-icon 
    .guardian-icon {
      width: 66px;
      height: 66px;
      margin-left: -30px;
    }
    
    .civilization-exp-value, .stamina-value, .guardian-level-text {
      color: #fff;
      font-size: 20px;
      font-weight: bold;
      margin-left: 5px;
    }
    
  
}


.top-right-buttons {
  display: flex;
  margin-right: auto;
  .guardian-avatar-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 140px;
  height: 140px;
}

.wings-decoration {
  // position: absolute;
  // left: 20px;
  // top: -20px;
  z-index: 1;
}

.wings-img {
  width: 140px;
  height: 140px;
  // filter: drop-shadow(0 4px 8px rgba(255, 105, 180, 0.4));
}

.guardian-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  /* z-index: 2; */
  // margin-top: 30px;
  position:absolute;
  top:10px;
  left:40px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.level-badge {
  position: absolute;
  bottom: 26px;
  z-index: 2;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 16px;
  font-weight: bold;
}
}

.top-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  
  .button-icon {
    width: 102px;
    height: 82px;
  }
  
  &:hover {
    transform: scale(1.1);
    // background: rgba(255, 255, 255, 1);
  }
  
  // &.active {
  //   background: rgba(52, 211, 153, 0.9);
  //   color: white;
    
  //   &:hover {
  //     background: rgba(52, 211, 153, 1);
  //   }
  // }
}

// BOSS区域
.boss-area {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 100px;
  background: rgba(255, 0, 0, 0.8);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.boss-label {
  font-size: $font-xxl;
  color: #fff;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}



:deep(.van-popup__close-icon--top-right){
  top: -16px!important;
  right: -16px!important;
  .van-icon__image{
      width: 66px;
      height: 70px;

    }
  
}


@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes lowStaminaAlert {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 攻击按钮样式 */
.bottom-box-2-item.charge-attack {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  font-weight: bold;
  animation: chargeReady 1.5s infinite alternate;
}

@keyframes chargeReady {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

.charge-bonus {
  font-size: 10px;
  margin-left: 5px;
  color: #ff6b35;
  font-weight: bold;
}




</style>