import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import i18n from './i18n'

// Vant 组件和样式（按需引入）
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'
import 'vant/es/image-preview/style'
import 'vant/es/switch/style'
import 'vant/es/icon/style'
import 'vant/es/button/style'

// 引入精确DPI适配器
import { initResponsiveAdapter } from './utils/responsive.js'

// 引入KrpanoManager并立即初始化全局回调
import krpanoManager from './utils/krpano.js'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(i18n)

// 隐藏加载遮罩的函数
function hideAppLoading() {
  const loadingEl = document.getElementById('app-loading')
  if (loadingEl) {
    loadingEl.classList.add('hide')
    // 动画完成后移除元素
    setTimeout(() => {
      if (loadingEl.parentNode) {
        loadingEl.parentNode.removeChild(loadingEl)
      }
    }, 300)
  }
}

// 初始化应用状态
const initApp =  async () => {
  try {
    // 1. 首先初始化数据服务
    const dataService = await import('./services/dataService.js').then(m => m.default)
    await dataService.initialize()
    console.log('数据服务初始化完成')

    // 2. 初始化响应式适配器
    initResponsiveAdapter()

    // 3. 加载认证状态（依赖 dataService）
    const { useAuthStore } = await import('./stores/auth.js')
    const authStore = useAuthStore()
    authStore.loadFromStorage()
    
    
    // 验证登录状态
    const isValidLogin = await authStore.validateLoginStatus()
    console.log('认证状态验证结果:', isValidLogin)

    // 4. 加载游戏状态（依赖 dataService）
    const { useGameStore } = await import('./stores/game.js')
    const gameStore = useGameStore()
    
    // 加载持久化的游戏状态
    const hasGameState = gameStore.loadGameState()
    if (hasGameState) {
      console.log('已恢复游戏状态')
    } else {
      console.log('无已保存的游戏状态')
    }
    
    // 加载收集数据（兼容旧版本）
    gameStore.loadCollections()

    console.log('应用初始化完成')
    
    // 5. 隐藏加载遮罩
    hideAppLoading()
  } catch (error) {
    console.error('应用初始化失败:', error)
    // 即使初始化失败也要隐藏遮罩
    hideAppLoading()
  }
}

// 初始化响应式适配器和应用状态
document.addEventListener('DOMContentLoaded', initApp)

app.mount('#app')