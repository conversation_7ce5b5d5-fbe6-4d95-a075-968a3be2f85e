/**
 * 游戏服务 - 封装游戏相关的API调用和业务逻辑
 */
import { api } from '@/utils/api'
import { useAuthStore } from '@/stores/auth'
import { useGameStore } from '@/stores/game'
import dataService from '@/services/dataService'
import deviceService from '@/services/deviceService'
import websocketService from '@/services/websocketService'
import guardianService from '@/services/guardianService'
import taskService from '@/services/taskService'
import staminaService from '@/services/staminaService'
import treasureBoxService from '@/services/treasureBoxService'
import bossService from '@/services/bossService'
import culturalQuizService from '@/services/cultQuiz'

class GameService {
  constructor() {
    this.currentSession = null
    this.isSessionActive = false
    this.websocketConnected = false
  }

  /**
   * 初始化游戏服务
   */
  async initialize() {
    try {
      // 检查后端健康状态
      // const health = await api.get('/health', { showLoading: false })
      // console.log('后端服务状态:', health.data)
      
      // 如果用户已登录，获取用户信息
      const authStore = useAuthStore()
      if (authStore.isLoggedIn) {
        dataService.resetGuestData()
        await this.refreshUserInfo()
      } else {
        // 如果没有登录，初始化游客模式
        this.initOfflineMode()
      }
      
      // 初始化WebSocket连接
      await this.initWebSocket()
      
      return true
    } catch (error) {
      console.error('游戏服务初始化失败:', error)
      // 在后端不可用时，启用离线模式
      this.initOfflineMode()
      return false
    }
  }

  /**
   * 初始化WebSocket连接
   */
  async initWebSocket() {
    try {
      const authStore = useAuthStore()
      const userId = authStore.user?.user_id || `guest_${Date.now()}`
      
      // 连接WebSocket
      await websocketService.connect(userId)
      this.websocketConnected = true
      
      // 设置事件监听器
      websocketService.on('connected', () => {
        console.log('WebSocket连接成功')
        this.websocketConnected = true
      })
      
      websocketService.on('disconnected', () => {
        console.log('WebSocket连接断开')
        this.websocketConnected = false
      })
      
      websocketService.on('miniprogram_pause', () => {
        console.log('收到小程序暂停指令')
        // 可以在这里处理游戏暂停逻辑
      })
      
      websocketService.on('miniprogram_resume', () => {
        console.log('收到小程序恢复指令')
        // 可以在这里处理游戏恢复逻辑
      })
      
      console.log('WebSocket服务初始化完成')
    } catch (error) {
      console.warn('WebSocket初始化失败:', error)
      this.websocketConnected = false
    }
  }

  /**
   * 检查是否在 CrazyGames 平台
   */
  isCrazyGamesPlatform() {
    // 检查 CrazyGames SDK 是否存在
    return !!(window.CrazyGames && window.CrazyGames.SDK)
  }

  /**
   * 初始化离线模式（游客模式）
   */
  initOfflineMode() {
    console.log('初始化离线模式')
    
    // 检查用户登录状态
    const authStore = useAuthStore()
    if (authStore.isLoggedIn) {
      console.log('用户已登录，不需要初始化游客模式')
      return
    }
    
    // 确保游客数据已初始化
    const guestData = dataService.getGuestData()
    
    // 加载游客数据到游戏状态
    const gameStore = useGameStore()
    gameStore.initGuestMode()
    
    console.log('离线模式已初始化，游客数据:', guestData)
  }

  /**
   * 游客登录
   */
  async guestLogin() {
    try {
      const deviceId = this.generateDeviceId()
      const deviceInfo = this.getDeviceInfo()
      
      const response = await api.auth.guestLogin(deviceId, deviceInfo)
      const { user_info, token, refresh_token } = response.data
      
      // 保存认证信息
      const authStore = useAuthStore()
      authStore.login({
        provider: 'guest',
        user: {
          ...user_info,
          isGuest: true
        },
        token,
        refreshToken: refresh_token
      })
      
      // 清除游客模式数据（已经登录了，不再是纯游客模式）
      dataService.clearGuestDataAfterLogin()
      console.log('游客登录成功，已清除本地游客数据')
      
      // 同步用户数据到游戏状态
      await this.syncUserDataToGameStore(user_info)
      
      console.log('游客登录成功:', user_info)
      return { success: true, user: user_info }
    } catch (error) {
      console.error('游客登录失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * CrazyGames 用户登录
   */
  async crazyGamesLogin(userToken) {
    try {
      console.log('尝试 CrazyGames 用户登录')

      const deviceId = this.generateDeviceId()
      const deviceInfo = this.getDeviceInfo()
      console.log(userToken, deviceId, deviceInfo,'----')
      const response = await api.auth.crazyGamesLogin(userToken, deviceId, deviceInfo)

      // 检查响应是否成功
      if (!response.data) {
        console.error('CrazyGames 登录失败：后端返回空数据')
        return { success: false, error: '后端返回空数据' }
      }

      const { user_id, token, refresh_token, user_info } = response.data

      // 验证必要字段
      if (!user_id || !token || !user_info) {
        console.error('CrazyGames 登录失败：缺少必要字段')
        return { success: false, error: '缺少必要字段' }
      }

      // 保存认证信息
      const authStore = useAuthStore()
      authStore.login({
        provider: 'crazygames',
        user: {
          user_id: user_id,
          ...user_info
        },
        token,
        refreshToken: refresh_token
      })

      // 清除游客模式状态（如果存在）
      const gameStore = useGameStore()
      if (dataService.isGuestMode()) {
        console.log('CrazyGames 登录成功，清除游客模式状态')
        dataService.resetGuestData()

        // 强制刷新游客模式状态检查
        console.log('游客模式状态已清除，当前状态:', dataService.isGuestMode())
      }

      // 同步用户数据到游戏状态
      await this.syncUserDataToGameStore(user_info)

      console.log('CrazyGames 用户登录成功:', user_info)
      console.log('用户登录状态:', authStore.isLoggedIn)
      console.log('游客模式状态:', gameStore.isGuestMode())

      return { success: true, user: user_info }
    } catch (error) {
      console.error('CrazyGames 用户登录失败:', error)

      // 返回详细的失败信息
      return {
        success: false,
        error: error.message || '网络连接失败',
        details: error
      }
    }
  }

  /**
   * 同步用户数据到游戏状态
   */
  async syncUserDataToGameStore(userInfo) {
    try {
      const gameStore = useGameStore()

      // 更新用户基本信息
      gameStore.updateUserInfo(userInfo)

      // 更新游戏资源
      gameStore.civilizationExp = userInfo.civilization_exp || 0
      gameStore.stamina = userInfo.stamina || 100

      // 尝试获取更多用户数据
      try {
        // 获取完整的用户资料信息
        const profileResponse = await api.user.getProfile()
        if (profileResponse.data) {
          console.log('用户资料信息同步成功:', profileResponse.data)

          // 更新更完整的用户信息
          gameStore.updateUserInfo(profileResponse.data)

          // 更新资源（使用最新数据）
          gameStore.civilizationExp = profileResponse.data.civilization_exp || gameStore.civilizationExp
          gameStore.stamina = profileResponse.data.stamina || gameStore.stamina
          
          // 更新文明守护者数据
          gameStore.userInfo.total_thieves_captured = profileResponse.data.total_thieves_captured || 0
          gameStore.userInfo.total_garbage_cleaned = profileResponse.data.total_garbage_cleaned || 0
          gameStore.userInfo.total_monuments_protected = profileResponse.data.total_monuments_protected || 0
          gameStore.userInfo.collections_count = profileResponse.data.collections_count || 0
          gameStore.userInfo.guardian_level = profileResponse.data.guardian_level || 1
        }
      } catch (profileError) {
        console.warn('获取用户资料失败，使用登录返回的数据:', profileError.message)
      }


      // 保存游戏状态
      gameStore.saveGameState()
      console.log('用户数据同步完成并已保存')
    } catch (error) {
      console.error('同步用户数据失败:', error)
      // 不抛出异常，允许登录继续
    }
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    try {
      const response = await api.user.getProfile()
      const userInfo = response.data
      
      // 更新认证状态
      const authStore = useAuthStore()
      authStore.updateUser(userInfo)
      
      // 更新游戏状态
      const gameStore = useGameStore()
      gameStore.userInfo = {
        ...gameStore.userInfo,
        user_id: userInfo.user_id,
        nickname: userInfo.nickname || '游客',
        level: userInfo.level || 1,
        exp: userInfo.exp || 0,
        vip_level: userInfo.vip_level || 0,
        avatar_url: userInfo.avatar_url,
        total_play_time: userInfo.total_play_time || 0,
        total_sessions: userInfo.total_sessions || 0,
        collections_count: userInfo.collections_count || 0,
        cities_unlocked: userInfo.cities_unlocked || 1,
        // 文明守护者数据
        total_thieves_captured: userInfo.total_thieves_captured || 0,
        total_garbage_cleaned: userInfo.total_garbage_cleaned || 0,
        total_monuments_protected: userInfo.total_monuments_protected || 0,
        guardian_level: userInfo.guardian_level || 1
      }
      gameStore.civilizationExp = userInfo.civilization_exp || gameStore.civilizationExp
      gameStore.stamina = userInfo.stamina || gameStore.stamina
      gameStore.maxStamina = userInfo.max_stamina || 100
      
      console.log('用户信息更新成功:', userInfo, {
        文明经验: gameStore.civilizationExp,
        体力: `${gameStore.stamina}/${gameStore.maxStamina}`,
        守望者等级: gameStore.userInfo.guardian_level,
        图鉴收集: gameStore.userInfo.collections_count
      })
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取城市列表
   */
  async getCities() {
    try {
      const response = await api.game.getCities()
      console.log('城市列表获取成功:', response.data)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取城市列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从krpano XML获取热点数据 - 使用krpano工具类
   */
  async getHotspotsFromKrpano() {
    try {
      // 使用krpano工具类，不依赖window.krpano
      const krpanoManager = await import('@/utils/krpano')

      if (!krpanoManager.default.ready) {
        return { thieves: [], garbage: [], treasure: [], boss: [] }
      }

      // 使用现成的方法获取热点
      const thiefHotspots = krpanoManager.default.getThiefHotspots()
      const garbageHotspots = krpanoManager.default.getGarbageHotspots()
      const quizHotspots = krpanoManager.default.getQuizHotspots()
      const allHotspots = krpanoManager.default.getAllHotspots()

      // 获取宝箱和boss热点
      const treasureHotspots = allHotspots.filter(h =>
        h.name.startsWith('treasure_') && h.visible && h.alpha > 0
      )
      const bossHotspots = allHotspots.filter(h =>
        h.name.startsWith('boss_') && h.visible && h.alpha > 0
      )

      const hotspots = {
        thieves: thiefHotspots.map(h => ({
          name: h.name,
          type: 'thief',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        garbage: garbageHotspots.map(h => ({
          name: h.name,
          type: 'garbage',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        quiz: quizHotspots.map(h => ({
          name: h.name,
          type: 'quiz',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        treasure: treasureHotspots.map(h => ({
          name: h.name,
          type: 'treasure',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        boss: bossHotspots.map(h => ({
          name: h.name,
          type: 'boss',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        }))
      }

      return hotspots
    } catch (error) {
      console.error('从krpano获取热点数据失败:', error)
      return { thieves: [], garbage: [], treasure: [], boss: [] }
    }
  }

  /**
   * 开始游戏会话
   */
  async startGameSession(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      // 检查用户登录状态
      const authStore = useAuthStore()
      const gameStore = useGameStore()
      
      // 从krpano获取热点数据
      const hotspotsFromXML = await this.getHotspotsFromKrpano()
      console.log('🔍 从XML获取的热点数据:', hotspotsFromXML)
      
      // 准备发送给后端的热点数据
      const allHotspots = [
        ...hotspotsFromXML.thieves,
        ...hotspotsFromXML.garbage,
        ...hotspotsFromXML.treasure,
        ...hotspotsFromXML.boss,
        ...hotspotsFromXML.quiz
      ]
      console.log('📤 发送给后端的热点数据:', allHotspots.length, '个热点')
      
      // 如果用户已登录，优先尝试在线会话（不管是否有游客模式标记）
      if (authStore.isLoggedIn) {
        console.log('用户已登录，尝试在线会话')
        try {
      const response = await api.game.startSession(cityId, sceneId, allHotspots)
      const { session_id, collected_hotspot_ids, collection_status, hotspot_progress } = response.data
      
      console.log('🔍 接口返回的收集状态:', { 
        session_id, 
        collected_hotspot_ids: collected_hotspot_ids?.length || 0, 
        collected_list: collected_hotspot_ids 
      })
      
      this.currentSession = {
        id: session_id,
        cityId,
        sceneId,
        startTime: Date.now(),
        isOffline: false
      }
      this.isSessionActive = true
      
      // 使用已收集热点ID列表过滤XML热点数据
      const collectedIds = new Set(collected_hotspot_ids || [])
      const availableHotspotsByType = {
        thieves: hotspotsFromXML.thieves.filter(h => !collectedIds.has(h.name)),
        garbage: hotspotsFromXML.garbage.filter(h => !collectedIds.has(h.name)),
        treasure: hotspotsFromXML.treasure.filter(h => !collectedIds.has(h.name)),
        boss: hotspotsFromXML.boss.filter(h => !collectedIds.has(h.name)),
        quiz: hotspotsFromXML.quiz.filter(h => !collectedIds.has(h.name))
      }
      
      gameStore.updateHotspotCounts({
        totalThieves: hotspotsFromXML.thieves.length,
        totalGarbage: hotspotsFromXML.garbage.length,
        totalTreasure: hotspotsFromXML.treasure.length,
        totalBoss: hotspotsFromXML.boss.length,
        totalQuiz: hotspotsFromXML.quiz.length,
        remainingThieves: availableHotspotsByType.thieves.length,
        remainingGarbage: availableHotspotsByType.garbage.length,
        remainingTreasure: availableHotspotsByType.treasure.length,
        remainingBoss: availableHotspotsByType.boss.length
      })
      
      // 保存收集状态到游戏存储
      gameStore.hotspotCollectionStatus = collection_status
      gameStore.collectedHotspotIds = collected_hotspot_ids || []
      
      // 保存过滤后的热点数据到gameStore（已排除收集的热点）
      gameStore.thieves = availableHotspotsByType.thieves
      gameStore.garbages = availableHotspotsByType.garbage
      gameStore.treasures = availableHotspotsByType.treasure
      gameStore.quizHotspots = availableHotspotsByType.quiz
      
      // 更新热点进度（使用后端返回的数据）
      gameStore.hotspotProgress = {
        total_count: hotspot_progress.total_count || 0,
        collected_count: hotspot_progress.collected_count || 0,
        remaining_count: (hotspot_progress.total_count || 0) - (hotspot_progress.collected_count || 0)
      }
      
      // 隐藏已收集的热点
      await this.hideCollectedHotspots(collected_hotspot_ids || [])
      
      console.log('在线游戏会话开始:', this.currentSession)
      console.log('✅ 热点状态已同步:', {
        总热点: hotspot_progress.total_count,
        已收集: hotspot_progress.collected_count,
        可用热点: Object.values(availableHotspotsByType).reduce((sum, arr) => sum + arr.length, 0)
      })
      
      // 发送游戏开始事件到WebSocket
      if (this.websocketConnected) {
        websocketService.sendGameStart(this.currentSession)
      }
      
      return {
        success: true,
        session: this.currentSession
          }
        } catch (error) {
          // 已登录用户的后端会话创建失败，仍然尝试离线模式作为备用
          console.warn('已登录用户的后端会话创建失败，使用离线模式作为备用:', error.message)
          return this.startOfflineSession(cityId, sceneId)
        }
      }
      
      // 如果用户未登录，使用纯游客模式
      if (!authStore.isLoggedIn) {
        console.log('用户未登录，使用纯游客模式（离线会话）')
        // 确保游客数据已初始化
        if (!dataService.getJSON('guestGameData')) {
          dataService.initGuestData()
          gameStore.initGuestMode()
        }
        return this.startOfflineSession(cityId, sceneId)
      }
    } catch (error) {
      console.error('开始游戏会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 开始离线游戏会话
   */
  startOfflineSession(cityId = 'beijing', sceneId = 'scene_level_1') {
    console.log('开始离线游戏会话')
    
    // 生成默认热点数据
    const defaultHotspots = this.generateDefaultHotspots()
    
    this.currentSession = {
      id: 'offline_' + Date.now(),
      cityId,
      sceneId,
      hotspots: defaultHotspots,
      startTime: Date.now(),
      isOffline: true
    }
    this.isSessionActive = true
    
    // 更新游戏状态中的热点数据
    const gameStore = useGameStore()
    gameStore.thieves = []
    gameStore.garbages = []
    gameStore.treasures = []
    
    // 根据热点类型分类
    defaultHotspots.forEach(hotspot => {
      const hotspotData = {
        id: hotspot.id,
        name: hotspot.name || hotspot.id,
        type: hotspot.type,
        position: hotspot.position,
        reward: hotspot.reward_preview,
        // 添加krpano所需的属性（离线模式默认值）
        image_url: hotspot.image_url || this.getDefaultImageUrl(hotspot.type),
        scale: hotspot.scale || this.getDefaultScale(hotspot.type),
        onclick_action: hotspot.onclick_action || this.getDefaultOnclickAction(hotspot.type, hotspot.name),
        visible: hotspot.visible !== false,
        description: hotspot.description || ''
      }
      
      switch (hotspot.type) {
        case 'thief':
        case 'boss_thief':
          gameStore.thieves.push(hotspotData)
          break
        case 'garbage':
          gameStore.garbages.push(hotspotData)
          break
        case 'treasure':
          gameStore.treasures.push(hotspotData)
          break
      }
    })
    
    console.log('离线游戏会话开始:', this.currentSession)
    console.log('热点分布:', {
      thieves: gameStore.thieves.length,
      garbages: gameStore.garbages.length,
      treasures: gameStore.treasures.length
    })
    
    return {
      success: true,
      session: this.currentSession
    }
  }

  /**
   * 生成默认热点数据（离线模式）
   */
  generateDefaultHotspots() {
    const hotspots = []
    
    // 生成小偷热点
    for (let i = 1; i <= 4; i++) {
      const name = `thief_${i}`
      hotspots.push({
        id: name,
        name: name,
        type: 'thief',
        position: { x: Math.random() * 360 - 180, y: Math.random() * 90 - 45 },
        reward_preview: {
          type: 'civilization_exp',
          amount: Math.floor(Math.random() * 15) + 5
        },
        image_url: '/img/thief/thief_1_320/thief1_1.png',
        scale: 0.8,
        onclick_action: `js(onThiefClicked('${name}'));`,
        visible: true,
        description: ''
      })
    }
    
    // 生成垃圾热点
    for (let i = 1; i <= 3; i++) {
      const name = `garbage_${i}`
      hotspots.push({
        id: name,
        name: name,
        type: 'garbage',
        position: { x: Math.random() * 360 - 180, y: Math.random() * 90 - 45 },
        reward_preview: {
          type: 'civilization_exp',
          amount: Math.floor(Math.random() * 12) + 3
        },
        image_url: '/img/icons/laji_icon.png',
        scale: 0.03,
        onclick_action: `js(onGarbageClicked('${name}'));`,
        visible: true,
        description: ''
      })
    }
    
    // 生成宝箱热点
    for (let i = 1; i <= 2; i++) {
      const name = `treasure_${i}`
      hotspots.push({
        id: name,
        name: name,
        type: 'treasure',
        position: { x: Math.random() * 360 - 180, y: Math.random() * 90 - 45 },
        reward_preview: {
          type: 'civilization_exp',
          amount: Math.floor(Math.random() * 25) + 10
        },
        image_url: '/img/page_icons/START-iocn.png',
        scale: 0.04,
        onclick_action: `js(onTreasureClicked('${name}'));`,
        visible: true,
        description: ''
      })
    }
    
    return hotspots
  }

  /**
   * 从热点名称推断热点类型
   */
  getHotspotTypeFromName(hotspotName) {
    if (hotspotName.startsWith('thief_')) {
      return 'thief'
    } else if (hotspotName.startsWith('garbage_')) {
      return 'garbage'
    } else if (hotspotName.startsWith('treasure_')) {
      return 'treasure'
    } else if (hotspotName.startsWith('boss_')) {
      return 'boss'
    } else {
      // 默认返回thief类型
      return 'thief'
    }
  }

  /**
   * 统一的热点收集方法
   * 支持所有类型热点：小偷、垃圾、宝箱、古迹
   */
  async collectHotspot(hotspotName, cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      if (!this.isSessionActive || !this.currentSession) {
        throw new Error('没有活跃的游戏会话')
      }

      const hotspotType = this.getHotspotTypeFromName(hotspotName)
      const gameStore = useGameStore()
      const authStore = useAuthStore()
      
      console.log(`🎯 开始收集热点: ${hotspotName} (类型: ${hotspotType})`)

      // 如果用户已登录且是在线会话，调用统一的后端API
      if (authStore.isLoggedIn && !this.currentSession.isOffline) {
        try {
          const response = await api.post('/api/v1/hotspot/collect', {
            hotspot_id: hotspotName,
            hotspot_type: hotspotType.toUpperCase(),
            city_id: cityId,
            scene_id: sceneId,
            session_id: this.currentSession.id
          })

          const result = response.data.data
          
          // 更新游戏状态
          if (result.rewards) {
            gameStore.addCivilizationExp(result.rewards.experience)
          }

          // 处理图鉴掉落
          if (result.artifact_drop && result.artifact_drop.artifact_id) {
            console.log('🎁 获得图鉴:', result.artifact_drop)
            
            // 添加到文化图鉴收集
            gameStore.addToCollection('artifacts', result.artifact_drop.artifact_id)
            
            // 显示图鉴获得通知
            this.showArtifactNotification(result.artifact_drop)
          }

          // 立即隐藏已收集的热点
          await this.hideCollectedHotspots([hotspotName])
          
          // 更新收集进度
          if (!gameStore.collectedHotspotIds.includes(hotspotName)) {
            gameStore.collectedHotspotIds.push(hotspotName)
          }
          
          // 更新热点进度显示
          if (result.progress) {
            const totalCount = gameStore.hotspotProgress.total_count || 0
            const collectedCount = result.progress.collected_count
            gameStore.hotspotProgress = {
              total_count: totalCount,
              collected_count: collectedCount,
              remaining_count: Math.max(0, totalCount - collectedCount)
            }
          }

          console.log('✅ 在线热点收集成功:', result)
          return { 
            success: true, 
            result,
            expGained: result.rewards.experience,
            culturalQuiz: result.cultural_quiz
          }

        } catch (error) {
          console.warn('在线热点收集失败，使用离线逻辑:', error.message)
          return this.collectOfflineHotspot(hotspotName, cityId)
        }
      } else {
        // 离线模式
        return this.collectOfflineHotspot(hotspotName, cityId)
      }

    } catch (error) {
      console.error('热点收集失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从热点名称推断热点类型
   */
  getHotspotTypeFromName(hotspotName) {
    if (hotspotName.startsWith('thief_')) {
      return 'thief'
    } else if (hotspotName.startsWith('garbage_')) {
      return 'garbage'
    } else if (hotspotName.startsWith('treasure_')) {
      return 'treasure'
    } else if (hotspotName.startsWith('boss_')) {
      return 'boss'
    } else if (hotspotName.startsWith('monument_')) {
      return 'monument'
    } else {
      // 默认返回thief类型
      return 'thief'
    }
  }

  /**
   * 离线模式热点收集
   */
  async collectOfflineHotspot(hotspotName, cityId = 'beijing') {
    try {
      const hotspotType = this.getHotspotTypeFromName(hotspotName)
      const gameStore = useGameStore()

      // 生成经验奖励
      const expRewards = {
        'thief': 15,
        'garbage': 12,
        'treasure': 25,
        'monument': 30,
        'boss': 50
      }
      const expReward = expRewards[hotspotType] || 10

      // 给予文明经验
      gameStore.addCivilizationExp(expReward)

      // 更新热点剩余数量
      gameStore.collectHotspotByType(hotspotType)

      // 更新收集统计
      if (hotspotType === 'thief') {
        gameStore.userInfo.total_thieves_captured = (gameStore.userInfo.total_thieves_captured || 0) + 1
      } else if (hotspotType === 'garbage') {
        gameStore.userInfo.total_garbage_cleaned = (gameStore.userInfo.total_garbage_cleaned || 0) + 1
      }

      // 隐藏已收集的热点
      await this.hideCollectedHotspots([hotspotName])
      
      // 更新收集进度
      if (!gameStore.collectedHotspotIds.includes(hotspotName)) {
        gameStore.collectedHotspotIds.push(hotspotName)
      }

      const result = {
        rewards: {
          experience: expReward,
          type: 'civilization_exp'
        },
        cultural_quiz: {
          should_trigger: false // 离线模式不触发问答
        }
      }

      console.log('✅ 离线热点收集成功:', result)
      
      return { 
        success: true, 
        result,
        expGained: expReward
      }
    } catch (error) {
      console.error('离线热点收集失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 结束游戏会话
   */
  async endGameSession() {
    try {
      if (!this.isSessionActive || !this.currentSession) {
        console.log('没有活跃的游戏会话需要结束')
        return { success: true }
      }

      // 如果是离线模式，不需要调用后端
      if (this.currentSession.isOffline) {
        this.currentSession = null
        this.isSessionActive = false
        console.log('离线游戏会话已结束')
        return { success: true }
      }

      const response = await api.game.endSession(this.currentSession.id)
      const result = response.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      if (result.summary) {
        if (result.summary.civilization_exp_earned) {
          gameStore.addCivilizationExp(result.summary.civilization_exp_earned)
        }
        if (result.summary.exp_earned) {
          gameStore.userInfo.exp += result.summary.exp_earned
          gameStore.checkLevelUp()
        }
        
        // 添加获得的文物到收集
        if (result.summary.artifacts_found && result.summary.artifacts_found.length > 0) {
          result.summary.artifacts_found.forEach(artifactId => {
            gameStore.addToCollection('artifacts', artifactId)
          })
        }
      }
      
      // 处理升级
      if (result.level_up) {
        gameStore.userInfo.level = result.level_up.new_level
        // 发放升级奖励
        if (result.level_up.rewards) {
          Object.entries(result.level_up.rewards).forEach(([type, amount]) => {
            if (type === 'civilization_exp') {
              gameStore.addCivilizationExp(amount)
            }
          })
        }
      }
      
      // 清理会话状态
      this.currentSession = null
      this.isSessionActive = false
      
      console.log('游戏会话结束:', result)
      return { success: true, result }
    } catch (error) {
      console.error('结束游戏会话失败:', error)
      return { success: false, error: error.message }
    }
  }





  /**
   * 获取文明经验排行榜
   */
  async getCivilizationExpLeaderboard(limit = 50, cityId = null) {
    try {
      const authStore = useAuthStore()
      
      if (!authStore.isLoggedIn) {
        // 离线模式使用模拟排行榜
        return await this.getOfflineRanking('civilization_exp', limit)
      }

      const response = await api.get('/ranking/civilization-exp', {
        params: { limit, city_id: cityId }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取文明经验排行榜失败:', error)
      // 失败时使用离线排行榜作为备用
      return await this.getOfflineRanking('civilization_exp', limit)
    }
  }

  /**
   * 获取图鉴收集排行榜
   */
  async getCollectionsLeaderboard(limit = 50, cityId = null) {
    try {
      const authStore = useAuthStore()
      
      if (!authStore.isLoggedIn) {
        return await this.getOfflineRanking('collections', limit)
      }

      const response = await api.get('/ranking/collections', {
        params: { limit, city_id: cityId }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取图鉴收集排行榜失败:', error)
      return await this.getOfflineRanking('collections', limit)
    }
  }

  /**
   * 获取综合排行榜
   */
  async getCombinedLeaderboard(limit = 50, cityId = null) {
    try {
      const authStore = useAuthStore()
      
      if (!authStore.isLoggedIn) {
        return await this.getOfflineRanking('combined', limit)
      }

      const response = await api.get('/ranking/combined', {
        params: { limit, city_id: cityId }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取综合排行榜失败:', error)
      return await this.getOfflineRanking('combined', limit)
    }
  }

  /**
   * 获取离线模拟排行榜
   */
  async getOfflineRanking(rankingType, limit = 50) {
    const mockRankings = []
    const gameStore = useGameStore()
    
    // 生成模拟排行榜数据
    for (let i = 1; i <= limit; i++) {
      const baseScore = Math.floor(Math.random() * 5000) + 500
      mockRankings.push({
        rank: i,
        user_id: `offline_user_${i}`,
        nickname: `守护者${i}`,
        avatar: `/avatars/default_${(i % 5) + 1}.png`,
        civilization_exp: rankingType === 'civilization_exp' ? baseScore : Math.floor(baseScore * 0.8),
        guardian_level: Math.min(10, Math.floor(baseScore / 500) + 1),
        collections_count: rankingType === 'collections' ? Math.floor(baseScore / 100) : Math.floor(baseScore / 150),
        combined_score: rankingType === 'combined' ? baseScore + Math.floor(baseScore / 10) : baseScore
      })
    }

    // 添加当前用户到排行榜
    const myRank = {
      rank: 25,
      user_id: 'current_user',
      nickname: gameStore.userInfo?.nickname || '我',
      avatar: gameStore.userInfo?.avatar_url || '/avatars/default_1.png',
      civilization_exp: gameStore.civilizationExp || 0,
      guardian_level: gameStore.userInfo?.guardian_level || 1,
      collections_count: gameStore.userInfo?.collections_count || 0
    }

    return {
      success: true,
      data: {
        ranking_type: rankingType,
        city_id: 'global',
        update_time: new Date().toISOString(),
        rankings: mockRankings,
        my_rank: myRank
      }
    }
  }

  /**
   * 获取排行榜（向后兼容旧接口）
   */
  async getLeaderboard(limit = 100) {
    try {
      // 默认返回图鉴收集排行榜以保持兼容性
      const result = await this.getCollectionsLeaderboard(limit)
      
      if (result.success) {
        // 转换数据格式以匹配旧接口
        const convertedData = {
          leaderboard_type: 'artifact_collector',
          update_time: result.data.update_time,
          my_rank: result.data.my_rank?.rank,
          rankings: result.data.rankings.map(item => ({
            rank: item.rank,
            user_id: item.user_id,
            nickname: item.nickname,
            artifacts_collected: item.collections_count,
            total_artifacts: 100,
            play_time: 0,
            cities_completed: 0,
            total_cities: 8
          }))
        }
        
        return { success: true, data: convertedData }
      }
      
      return result
    } catch (error) {
      console.error('获取排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取玩家进度
   */
  async getPlayerProgress(userId) {
    try {
      const authStore = useAuthStore()
      
      // 如果是未登录用户，从本地数据获取
      if (!authStore.isLoggedIn) {
        console.log('未登录用户，使用本地游戏进度')
        const guestData = dataService.getGuestData()
        
        return {
          artifacts_collected: guestData.stats.total_artifacts || 0,
          rank: 999, // 游客默认排名
          level: guestData.userInfo.level || 1,
          exp: guestData.userInfo.exp || 0,
          total_score: guestData.stats.total_score || 0
        }
      }

      // 尝试从后端API获取玩家详细信息
      const response = await api.leaderboard.getPlayerDetails(userId)
      console.log('玩家进度数据获取成功:', response.data)
      
      // 转换API返回的数据格式
      const playerData = response.data
      
      // 计算总的文物收集数量（从各城市进度累加）
      let totalArtifacts = 0
      if (playerData.city_progress && Array.isArray(playerData.city_progress)) {
        totalArtifacts = playerData.city_progress.reduce((sum, city) => {
          return sum + (city.artifacts_collected || 0)
        }, 0)
      }
      
      const result = {
        artifacts_collected: totalArtifacts,
        total_artifacts: 320, // 总共320个文物（根据后端设定）
        rank: 999, // 排名需要从排行榜API获取
        level: playerData.player_info?.level || 1,
        exp: 0,
        total_score: playerData.player_info?.total_play_time || 0,
        cities_guarded: playerData.collection_stats?.cities_guarded || 0,
        total_cities: playerData.collection_stats?.total_cities || 0
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.warn('获取玩家进度失败，使用本地数据:', error.message)
      
      // API失败时，从游戏状态获取数据
      const gameStore = useGameStore()
      const authStore = useAuthStore()
      
      return {
        success: true,
        data: {
          artifacts_collected: gameStore.userInfo?.total_artifacts || 
                              authStore.userInfo?.total_artifacts || 0,
          rank: 999,
          level: gameStore.userInfo?.level || authStore.userInfo?.level || 1,
          exp: gameStore.userInfo?.exp || authStore.userInfo?.exp || 0,
          total_score: gameStore.userInfo?.total_score || 0
        }
      }
    }
  }

  /**
   * 获取每日任务
   */
  async getDailyTasks() {
    try {
      const response = await api.task.getDailyTasks()
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取每日任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 领取任务奖励
   */
  async claimTaskReward(taskId) {
    try {
      const response = await api.task.claimTaskReward(taskId)
      const result = response.data
      
      // 更新用户资源（如果后端返回了奖励数据）
      if (result.rewards) {
        const gameStore = useGameStore()
        Object.entries(result.rewards).forEach(([resource, amount]) => {
          if (resource === 'civilization_exp') {
            gameStore.addCivilizationExp(amount)
          }
        })
      }
      
      console.log('任务奖励领取结果:', result)
      return { success: true, result }
    } catch (error) {
      console.error('领取任务奖励失败:', error)
      return { success: false, error: error.message }
    }
  }


  /**
   * 重置用户场景热点 - 完整的重新开始游戏功能
   */
  async resetUserSceneHotspots(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      const response = await api.game.resetUserSceneHotspots(cityId, sceneId)
      const result = response.data

      return { success: true, data: result }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 按关卡类型重置热点收集状态
   * @param {string} levelType - 关卡类型：thief, garbage, quiz
   * @param {string} cityId - 城市ID 
   * @param {string} sceneId - 场景ID
   */
  async resetLevelHotspots(levelType, cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      const response = await api.post('/hotspots/level/reset', {
        level_type: levelType,
        city_id: cityId,
        scene_id: sceneId
      })
      
      if (response.data.success) {
        console.log(`✅ ${levelType}关卡重置成功:`, response.data.data)
        
        // 重新启动游戏会话以获取最新的热点状态
        const sessionResult = await this.startGameSession(cityId, sceneId)
        
        return {
          success: true,
          message: response.data.message,
          reset_data: response.data.data,
          session_result: sessionResult
        }
      } else {
        return {
          success: false,
          error: response.data.message || '重置关卡失败'
        }
      }
      
    } catch (error) {
      console.error(`重置${levelType}关卡失败:`, error)
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '重置关卡失败'
      }
    }
  }

  /**
   * 获取各关卡的完成状态
   * @param {string} cityId - 城市ID
   * @param {string} sceneId - 场景ID  
   */
  async getLevelCompletionStatus(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      const response = await api.get(`/hotspots/level/status/${cityId}/${sceneId}`)
      
      if (response.data.success) {
        return {
          success: true,
          level_stats: response.data.level_stats,
          total_collected: response.data.total_collected
        }
      } else {
        return {
          success: false,
          error: '获取关卡状态失败'
        }
      }
      
    } catch (error) {
      console.error('获取关卡状态失败:', error)
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '获取关卡状态失败'
      }
    }
  }

  /**
   * 获取用户图鉴收集状态
   * @param {string} cityId - 城市ID
   */
  async getUserArtifactsStatus(cityId = 'beijing') {
    try {
      const response = await api.get(`/hotspots/level/artifacts/${cityId}`)
      
      if (response.data.success) {
        return {
          success: true,
          total_artifacts: response.data.total_artifacts,
          artifacts: response.data.artifacts
        }
      } else {
        return {
          success: false,
          error: '获取图鉴状态失败'
        }
      }
      
    } catch (error) {
      console.error('获取图鉴状态失败:', error)
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '获取图鉴状态失败'
      }
    }
  }

  /**
   * 重新开始游戏 - 重置所有热点收集状态并重新加载热点
   */
  async restartGame(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      const gameStore = useGameStore()
      const authStore = useAuthStore()

      console.log('🔄 开始重新游戏...')

      // 1. 如果用户已登录，重置后端数据
      if (authStore.isLoggedIn) {
        console.log('📡 重置后端热点收集数据...')
        const resetResult = await this.resetHotspotCollections(cityId, sceneId)
        
        if (!resetResult.success) {
          console.warn('后端重置失败，继续前端重置:', resetResult.error)
        } else {
          console.log('✅ 后端数据重置成功:', resetResult.data)
        }
      }

      // 2. 重置前端游戏状态
      console.log('🎮 重置前端游戏状态...')
      gameStore.collectedHotspotIds = []
      gameStore.hotspotCollectionStatus = {}
      gameStore.hotspotProgress = {
        total_count: 0,
        collected_count: 0,
        remaining_count: 0
      }

      // 3. 重新获取所有XML热点数据
      console.log('📋 重新获取XML热点数据...')
      const hotspotsFromXML = await this.getHotspotsFromKrpano()
      console.log('🔍 获取到热点数据:', hotspotsFromXML)
      
      const allHotspots = [
        ...hotspotsFromXML.thieves,
        ...hotspotsFromXML.garbage,
        ...hotspotsFromXML.treasure,
        ...hotspotsFromXML.boss,
        ...hotspotsFromXML.quiz
      ]
      console.log('📤 准备发送给后端的热点数据:', allHotspots.length, '个热点')

      // 4. 重新加载所有热点到界面
      console.log('🎯 重新加载热点到界面...')
      await this.reloadAllHotspotsToInterface(hotspotsFromXML)

      // 5. 重启游戏会话并传递完整热点数据
      console.log('🚀 重启游戏会话并同步热点数据...')
      const sessionResult = await this.startGameSession(cityId, sceneId)
      
      if (sessionResult.success) {
        console.log('✅ 游戏重新开始成功!')
        console.log('📊 新的热点状态:', {
          总热点: gameStore.hotspotProgress.total_count,
          已收集: gameStore.hotspotProgress.collected_count,
          剩余: gameStore.hotspotProgress.remaining_count
        })
        
        return { 
          success: true, 
          message: '游戏已重新开始，所有热点重新可见',
          session: sessionResult.session,
          hotspots: {
            total: allHotspots.length,
            byType: {
              thieves: hotspotsFromXML.thieves.length,
              garbage: hotspotsFromXML.garbage.length,
              treasure: hotspotsFromXML.treasure.length,
              boss: hotspotsFromXML.boss.length,
              quiz: hotspotsFromXML.quiz.length
            }
          }
        }
      } else {
        throw new Error('重启游戏会话失败: ' + sessionResult.error)
      }

    } catch (error) {
      console.error('❌ 重新开始游戏失败:', error)
      return { 
        success: false, 
        error: error.message,
        message: '重新开始游戏失败，请刷新页面重试'
      }
    }
  }

  /**
   * 重新加载所有热点到界面
   */
  async reloadAllHotspotsToInterface(hotspotsFromXML) {
    try {
      console.log('🔄 开始重新加载热点到界面...')
      
      // 使用krpano工具类重新显示所有热点
      const krpanoManager = await import('@/utils/krpano')
      
      if (!krpanoManager.default.ready) {
        console.warn('⚠️ krpano未准备就绪，无法重新加载热点')
        return { success: false, error: 'krpano未准备就绪' }
      }

      let reloadedCount = 0
      
      // 重新加载各类型热点
      const hotspotTypes = ['thieves', 'garbage', 'treasure', 'boss', 'quiz']
      
      for (const type of hotspotTypes) {
        const hotspots = hotspotsFromXML[type] || []
        
        for (const hotspot of hotspots) {
          try {
            // 如果热点之前被隐藏了，重新显示它
            const hotspotName = hotspot.name
            
            // 使用krpano API重新显示热点（设置visible=true, alpha=1）
            krpanoManager.default.call(`
              if(hotspot[${hotspotName}],
                set(hotspot[${hotspotName}].visible, true);
                set(hotspot[${hotspotName}].alpha, 1);
                set(hotspot[${hotspotName}].enabled, true);
              );
            `)
            
            reloadedCount++
            console.log(`✅ 重新显示热点: ${hotspotName}`)
          } catch (error) {
            console.warn(`重新显示热点失败 ${hotspot.name}:`, error)
          }
        }
      }

      console.log(`✅ 总计重新显示 ${reloadedCount} 个热点`)
      return { success: true, reloadedCount }
      
    } catch (error) {
      console.error('重新加载热点到界面失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 重置热点收集状态（调用后端API）
   */
  async resetHotspotCollections(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      // 调用之前实现的热点收集服务重置方法
      const response = await api.post('/api/v1/hotspot/reset', {
        city_id: cityId,
        scene_id: sceneId
      })

      return { success: true, data: response.data }
    } catch (error) {
      console.error('重置热点收集状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取热点类型的默认图片URL
   */
  getDefaultImageUrl(type) {
    const defaults = {
      'thief': '/img/thief/thief_1_320/thief1_1.png',
      'boss_thief': '/img/thief/thief_1_320/thief1_1.png',
      'garbage': '/img/icons/laji_icon.png',
      'treasure': '/img/gold.png'
    }
    return defaults[type] || '/img/hotspot_default.png'
  }

  /**
   * 获取热点类型的默认缩放值
   */
  getDefaultScale(type) {
    const defaults = {
      'thief': 0.8,
      'boss_thief': 0.8,
      'garbage': 0.03,
      'treasure': 0.04
    }
    return defaults[type] || 0.5
  }

  /**
   * 获取热点类型的默认点击动作
   */
  getDefaultOnclickAction(type, name) {
    const actions = {
      'thief': `js(onThiefClicked('${name}'));`,
      'boss_thief': `js(onThiefClicked('${name}'));`,
      'garbage': `js(onGarbageClicked('${name}'));`,
      'treasure': `js(onTreasureClicked('${name}'));`
    }
    return actions[type] || `js(onHotspotClicked('${name}'));`
  }

  /**
   * 处理PRD系统的收集逻辑
   * 集成BOSS血量系统、宝箱系统、经验系统等
   */
  async handlePRDCollectionLogic(hotspotType, cityId, hotspotName, count = 1) {
    try {
      const gameStore = useGameStore()
      
      // 1. 根据收集行为类型获取对应的行为标识
      const actionTypeMap = {
        'thief': 'catch_thief',
        'boss_thief': 'catch_thief',
        'garbage': 'clean_rubbish',
        'rubbish': 'clean_rubbish'
      }
      
      const actionType = actionTypeMap[hotspotType]
      if (!actionType) {
        console.log('非标准收集行为，跳过PRD逻辑处理')
        return { success: true, skipped: true }
      }

      // 2. 处理BOSS血量影响
      const bossResult = await bossService.handleCollectionDamage(
        cityId, actionType, count, hotspotName
      )
      
      if (bossResult.success) {
        console.log('BOSS血量更新:', bossResult.data)
        
        // 更新游戏状态中的BOSS信息
        if (bossResult.data.boss_status) {
          gameStore.updateBossStatus(bossResult.data.boss_status)
        }
      }

      // 3. 处理宝箱掉落
      const treasureResult = await treasureBoxService.handleTreasureBoxDrop(
        actionType, count, cityId, {
          hotspot_name: hotspotName,
          session_id: this.currentSession?.sessionId
        }
      )
      
      if (treasureResult.success && treasureResult.data.total_drops > 0) {
        console.log('宝箱掉落:', treasureResult.data.dropped_boxes)
        
        // 显示宝箱获得提示
        this.showTreasureBoxNotification(treasureResult.data.dropped_boxes)
      }

      // 4. 检查BOSS是否被击败，触发金宝箱
      if (bossService.isBossDefeated()) {
        console.log('BOSS被击败！触发金宝箱')
        
        const bossDefeatResult = await treasureBoxService.handleTreasureBoxDrop(
          'boss_defeat', 1, cityId, {
            defeat_source: actionType,
            hotspot_name: hotspotName,
            session_id: this.currentSession?.sessionId
          }
        )
        
        if (bossDefeatResult.success) {
          console.log('BOSS击败金宝箱:', bossDefeatResult.data)
          this.showBossDefeatNotification(bossDefeatResult.data)
        }

        // 显示BOSS击败特效
        this.showBossDefeatEffect(cityId)
      }

      // 5. 体力消耗
      const staminaCost = this.getStaminaCostByAction(actionType)
      if (staminaCost > 0) {
        const staminaResult = await api.stamina.consume(
          staminaCost,
          actionType,
          hotspotName
        )
        
        if (staminaResult.data) {
          gameStore.updateStamina(staminaResult.data.current_stamina)
        }
      }

      // 6. 经验值奖励
      const expReward = this.getExperienceRewardByAction(actionType)
      if (expReward > 0) {
        const expResult = await api.experience.addExperience(
          expReward,
          actionType,
          hotspotName
        )
        
        if (expResult.data) {
          gameStore.updateExperience(expResult.data)
        }
      }

      return {
        success: true,
        data: {
          boss_result: bossResult.data,
          treasure_result: treasureResult.data,
          boss_defeated: bossService.isBossDefeated(),
          experience_gained: expReward,
          stamina_cost: staminaCost
        }
      }
    } catch (error) {
      console.error('PRD收集逻辑处理失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取行为对应的体力消耗
   */
  getStaminaCostByAction(actionType) {
    const staminaCosts = {
      'catch_thief': 1,
      'clean_rubbish': 1,
      'monument_quiz': 5
    }
    return staminaCosts[actionType] || 0
  }

  /**
   * 获取行为对应的经验奖励
   */
  getExperienceRewardByAction(actionType) {
    const expRewards = {
      'catch_thief': 12,
      'clean_rubbish': 8,
      'monument_quiz': 35
    }
    return expRewards[actionType] || 0
  }

  /**
   * 显示宝箱获得通知
   */
  showTreasureBoxNotification(boxes) {
    boxes.forEach(box => {
      console.log(`获得${box.box_type}宝箱: ${box.box_id}`)
      // 这里可以添加UI通知逻辑
    })
  }

  // 🚫 PRD合规性清理：移除BOSS击败通知
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  /**
   * 显示BOSS击败特效
   */
  showBossDefeatEffect(cityId) {
    console.log(`城市${cityId}的BOSS被击败，显示特效`)
    // 这里可以添加特效逻辑
  }

  /**
   * 更新热点收集进度
   */
  updateHotspotProgress() {
    try {
      const gameStore = useGameStore()
      
      // 获取当前已收集数和总数
      const collectedCount = gameStore.collectedHotspotIds.length
      const totalHotspots = gameStore.hotspotProgress.total_count || 0
      const remainingCount = Math.max(0, totalHotspots - collectedCount)
      
      // 更新游戏状态
      gameStore.hotspotProgress = {
        total_count: totalHotspots,
        collected_count: collectedCount,
        remaining_count: remainingCount
      }
      
      console.log(`📊 热点进度更新: ${collectedCount}/${totalHotspots} (剩余: ${remainingCount})`)
      
      return {
        success: true,
        total: totalHotspots,
        collected: collectedCount,
        remaining: remainingCount
      }
    } catch (error) {
      console.error('更新热点进度失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 隐藏已收集的热点
   */
  async hideCollectedHotspots(collectedHotspotIds) {
    try {
      if (!collectedHotspotIds || collectedHotspotIds.length === 0) {
        console.log('📍 没有已收集的热点需要隐藏')
        return { success: true }
      }

      // 使用krpano工具类隐藏热点
      const krpanoManager = await import('@/utils/krpano')
      
      if (!krpanoManager.default.isReady()) {
        console.warn('⚠️ krpano未准备就绪，延迟隐藏热点')
        // 延迟重试，等待krpano准备就绪
        setTimeout(() => {
          this.hideCollectedHotspots(collectedHotspotIds)
        }, 2000)
        return { success: false, error: 'krpano未准备就绪，延迟重试' }
      }

      let hiddenCount = 0
      console.log(`🎯 开始隐藏已收集的热点: [${collectedHotspotIds.join(', ')}]`)
      
      for (const hotspotId of collectedHotspotIds) {
        try {
          // 首先检查热点是否存在
          const hotspotExists = krpanoManager.default.get(`hotspot[${hotspotId}]`)
          if (hotspotExists) {
            // 设置热点为不可见和不可点击
            krpanoManager.default.call(`
              set(hotspot[${hotspotId}].visible, false);
              set(hotspot[${hotspotId}].enabled, false);
            `)
            hiddenCount++
            console.log(`🙈 已隐藏热点: ${hotspotId}`)
          } else {
            console.warn(`⚠️ 热点不存在: ${hotspotId}`)
          }
        } catch (error) {
          console.warn(`隐藏热点失败 ${hotspotId}:`, error)
        }
      }

      console.log(`✅ 总计隐藏 ${hiddenCount} 个已收集的热点`)
      return { success: true, hiddenCount }
    } catch (error) {
      console.error('隐藏已收集热点失败:', error)
      return { success: false, error: error.message }
    }
  }


  /**
   * 开始文化问答会话
   */
  async startCulturalQuiz(cityId, monumentId = null, difficulty = 'medium') {
    try {
      const result = await culturalQuizService.startQuizSession(cityId, monumentId, difficulty)
      
      if (result.success) {
        console.log('文化问答会话开始:', result.data)
        return result
      }
      
      return result
    } catch (error) {
      console.error('开始文化问答失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 提交文化问答答案
   */
  async submitCulturalQuizAnswers(cityId, answers, sessionId = null, monumentId = null) {
    try {
      // 验证答案格式
      const validation = culturalQuizService.validateAnswers(answers)
      if (!validation.valid) {
        return { success: false, error: validation.error }
      }

      const result = await culturalQuizService.submitAnswers(cityId, answers, sessionId, monumentId)
      
      if (result.success) {
        console.log('文化问答提交成功:', result.data)
        
        // 处理问答完成后的逻辑（BOSS攻击、宝箱掉落等）
        const completionResult = await culturalQuizService.handleQuizCompletion(result.data, cityId)
        
        return {
          success: true,
          data: {
            quiz_result: result.data,
            completion_result: completionResult.data
          }
        }
      }
      
      return result
    } catch (error) {
      console.error('提交文化问答答案失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 显示图鉴获得通知
   */
  showArtifactNotification(artifactInfo) {
    try {
      // 创建通知元素
      const notification = document.createElement('div')
      notification.className = 'artifact-notification'
      notification.innerHTML = `
        <div class="artifact-notification-content">
          <div class="artifact-icon">📜</div>
          <div class="artifact-text">
            <div class="artifact-title">获得图鉴！</div>
            <div class="artifact-name">${artifactInfo.name || artifactInfo.artifact_id}</div>
            <div class="artifact-rarity rarity-${artifactInfo.rarity || 'common'}">${this.getRarityText(artifactInfo.rarity)}</div>
          </div>
        </div>
      `
      
      // 添加样式
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 10000;
        animation: slideInRight 0.5s ease-out;
        font-family: Arial, sans-serif;
        max-width: 300px;
      `
      
      // 添加动画样式
      const style = document.createElement('style')
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        .artifact-notification-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .artifact-icon {
          font-size: 24px;
          flex-shrink: 0;
        }
        .artifact-title {
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 4px;
        }
        .artifact-name {
          font-size: 14px;
          margin-bottom: 4px;
        }
        .artifact-rarity {
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 4px;
          display: inline-block;
        }
        .rarity-common { background-color: #95a5a6; }
        .rarity-rare { background-color: #3498db; }
        .rarity-epic { background-color: #9b59b6; }
        .rarity-legendary { background-color: #f39c12; }
      `
      document.head.appendChild(style)
      
      // 添加到页面
      document.body.appendChild(notification)
      
      // 3秒后自动消失
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = 'slideInRight 0.5s ease-out reverse'
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification)
            }
          }, 500)
        }
      }, 3000)
      
      console.log('📜 图鉴通知已显示')
    } catch (error) {
      console.error('显示图鉴通知失败:', error)
    }
  }

  /**
   * 获取稀有度文本
   */
  getRarityText(rarity) {
    const rarityMap = {
      'common': '普通',
      'rare': '稀有', 
      'epic': '史诗',
      'legendary': '传说'
    }
    return rarityMap[rarity] || '普通'
  }
}

// 创建单例实例
export const gameService = new GameService()

// 默认导出
export default gameService
