/**
 * 热点服务
 * 实现热点数据的懒加载、缓存管理和动态同步
 */
import { api } from '@/utils/api'
import krpanoManager from '@/utils/krpano'
import { useGameStore } from '@/stores/game'

class HotspotService {
  constructor() {
    this.krpanoManager = null
    this.gameStore = null
    this.currentLayerCache = new Map()
    this.loadedLayers = new Set()
    this.viewportUpdateTimer = null
    this.lastViewUpdate = 0
    this.isInitialized = false
  }

  /**
   * 初始化热点服务
   */
  async initialize() {
    if (this.isInitialized) return
    
    try {
      // 使用导入的krpano管理器实例
      this.krpanoManager = krpanoManager
      this.gameStore = useGameStore()
      this.isInitialized = true
      console.log('热点服务初始化完成')
    } catch (error) {
      console.error('热点服务初始化失败:', error)
    }
  }


  /**
   * 从后端获取分层热点数据
   */
  async fetchLayeredHotspots(requestData) {
    try {
      const response = await api.hotspots.getLayeredHotspots(requestData)
      return response.data
    } catch (error) {
      console.error('获取分层热点失败:', error)
      
      // 后备方案：使用数据库热点
      return await this.fetchDatabaseHotspots(requestData.city_id, requestData.scene_id)
    }
  }

  /**
   * 从数据库获取热点数据（后备方案）
   */
  async fetchDatabaseHotspots(cityId, sceneId) {
    try {
      const response = await api.hotspots.getDatabaseHotspots(cityId, sceneId, {
        enabled_only: true,
        include_collected: false
      })
      return {
        success: response.data.success,
        hotspots: response.data.hotspots || [],
        layer_info: {
          source: 'database',
          total_hotspots: response.data.total_hotspots
        }
      }
    } catch (error) {
      console.error('获取数据库热点失败:', error)
      return { success: false, hotspots: [], layer_info: {} }
    }
  }

  /**
   * 获取krpano格式的热点配置
   */
  async fetchKrpanoHotspots(cityId, sceneId) {
    try {
      const response = await api.hotspots.getKrpanoHotspots(cityId, sceneId)
      return {
        success: response.data.success,
        hotspots: response.data.hotspots || [],
        layer_info: {
          source: 'krpano',
          total_hotspots: response.data.total_hotspots
        }
      }
    } catch (error) {
      console.error('获取krpano热点失败:', error)
      return { success: false, hotspots: [], layer_info: {} }
    }
  }

  /**
   * 同步XML配置到数据库
   */
  async syncXmlToDatabase(sceneFilePath = null, forceUpdate = false) {
    try {
      const response = await api.hotspots.syncXmlHotspots({
        scene_file_path: sceneFilePath,
        force_update: forceUpdate,
        sync_all: !sceneFilePath
      })

      if (response.data.success) {
        // 清除本地缓存
        this.clearCache()

        console.log('XML热点同步成功:', response.data)
        return response.data
      } else {
        throw new Error(response.data.message || '同步失败')
      }

    } catch (error) {
      console.error('XML热点同步失败:', error)
      throw error
    }
  }

  /**
   * 直接从管理后台配置加载热点（使用XML热点，不获取API数据）
   */
  async loadManagedHotspots(cityId, sceneId) {
    console.log('使用XML热点配置，跳过API数据获取:', { cityId, sceneId })
    // 返回成功状态，但不包含热点数据，因为使用XML中的配置
    return {
      success: true,
      hotspots: [],
      source: 'xml',
      message: '使用XML中的热点配置'
    }
  }

  /**
   * 刷新热点配置（使用XML热点，不获取API数据）
   */
  async refreshManagedHotspots() {
    console.log('使用XML热点配置，无需刷新API数据')
    // XML热点配置是静态的，无需刷新
    // 如果需要重新加载XML，可以重新加载场景
    return { success: true, source: 'xml' }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.currentLayerCache.clear()
    this.loadedLayers.clear()
    console.log('热点缓存已清除')
  }

  /**
   * 生成图层缓存键
   */
  generateLayerKey(viewCenter, viewRange, zoomLevel) {
    return `layer_${zoomLevel}_${Math.round(viewCenter.x)}_${Math.round(viewCenter.y)}_${Math.round(viewRange.width)}`
  }


  /**
   * 获取热点图片URL
   */
  getHotspotImageUrl(hotspotType, hotspotName) {
    const imageMap = {
      thief: () => {
        // 根据热点名称返回对应的小偷图片
        const match = hotspotName.match(/thief_(\d+)/)
        const index = match ? match[1] : '1'
        return `/img/thief/thief_1_320/thief1_${index}.png`
      },
      garbage: () => '/img/icons/laji_icon.png',
      treasure: () => '/img/treasure_box.png',
      boss_thief: () => '/img/thief/boss_thief.png'
    }

    const urlFunction = imageMap[hotspotType]
    return urlFunction ? urlFunction() : '/img/guaqia_cities_BG.png'
  }

  /**
   * 获取原始onclick事件
   */
  getOriginalOnclick(hotspotName, hotspotType = null) {
    const onclickMap = {
      thief: (name) => `js(onThiefClicked('${name}'));`,
      garbage: (name) => `js(onGarbageClicked('${name}'));`,
      treasure: (name) => `js(onTreasureClicked('${name}'));`,
      boss_thief: (name) => `js(onBossThiefClicked('${name}'));`
    }
    
    // 优先使用传入的类型，否则根据热点名称判断类型
    let type = hotspotType || 'thief'
    if (!hotspotType) {
      if (hotspotName.includes('garbage') || hotspotName.includes('laji')) {
        type = 'garbage'
      } else if (hotspotName.includes('treasure') || hotspotName.includes('gold')) {
        type = 'treasure'
      } else if (hotspotName.includes('boss')) {
        type = 'boss_thief'
      }
    }

    return onclickMap[type](hotspotName)
  }

  /**
   * 预加载相邻图层
   */
  async preloadAdjacentLayers(currentViewCenter, currentViewRange, currentZoomLevel) {
    try {
      // 计算相邻区域
      const adjacentAreas = this.calculateAdjacentAreas(currentViewCenter, currentViewRange)
      
      // 预加载相邻缩放级别的数据
      const preloadPromises = []
      
      for (const area of adjacentAreas) {
        const layerKey = this.generateLayerKey(area.center, area.range, currentZoomLevel)
        
        if (!this.loadedLayers.has(layerKey)) {
          preloadPromises.push(
            this.fetchLayeredHotspots({
              city_id: 'beijing',
              scene_id: 'scene_level_1',
              view_center: area.center,
              view_range: area.range,
              zoom_level: currentZoomLevel
            }).then(data => {
              if (data.success) {
                this.currentLayerCache.set(layerKey, data)
                this.loadedLayers.add(layerKey)
              }
            }).catch(error => {
              console.warn('预加载图层失败:', error)
            })
          )
        }
      }

      if (preloadPromises.length > 0) {
        await Promise.all(preloadPromises)
        console.log(`预加载完成 ${preloadPromises.length} 个相邻图层`)
      }

    } catch (error) {
      console.error('预加载相邻图层失败:', error)
    }
  }

  /**
   * 计算相邻区域
   */
  calculateAdjacentAreas(center, range) {
    const areas = []
    const offsetX = range.width * 0.5
    const offsetY = range.height * 0.5

    // 上下左右四个相邻区域
    const directions = [
      { x: 0, y: offsetY },      // 上
      { x: 0, y: -offsetY },     // 下
      { x: offsetX, y: 0 },      // 右
      { x: -offsetX, y: 0 }      // 左
    ]

    directions.forEach(offset => {
      areas.push({
        center: {
          x: center.x + offset.x,
          y: center.y + offset.y
        },
        range: range
      })
    })

    return areas
  }
}

// 创建全局热点服务实例
const hotspotService = new HotspotService()

export default hotspotService 