import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 自动导入 Vue 3 组合式 API
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'vue-i18n',
        'pinia',
        {
          'vant': [
            'showToast',
            'showDialog', 
            'showConfirmDialog',
            'showNotify',
            'showImagePreview',
            'showLoadingToast',
            'showSuccessToast',
            'showFailToast'
          ]
        }
      ],
      dts: true, // 生成类型声明文件
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true,
      },
    }),
    // 自动导入组件
    Components({
      resolvers: [VantResolver()],
      dts: true, // 生成类型声明文件
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0', // 监听所有网络接口（局域网内其他设备可访问）
    port: 3000,
    // host: true,
    open: true,
    proxy: {
      // 定义一个代理前缀，例如 '/remote-assets'
      '/remote-assets': {
        // 目标服务器地址
        target: 'https://pf.bigpixel.cn',
        // 必须开启，改变请求源头
        changeOrigin: true,
        // 重写路径，去掉我们自定义的代理前缀
        rewrite: (path) => path.replace(/^\/remote-assets/, ''),
        headers: {
          'Referer': 'https://pf.bigpixel.cn',
          'Origin': 'https://pf.bigpixel.cn'
        }
      },
      '/remote-asset': {
        // 目标服务器地址
        target: 'http://pf.bigpixel.cn',
        // 必须开启，改变请求源头
        changeOrigin: true,
        // 重写路径，去掉我们自定义的代理前缀
        rewrite: (path) => path.replace(/^\/remote-asset/, ''),
        headers: {
          'Referer': 'http://pf.bigpixel.cn',
          'Origin': 'http://pf.bigpixel.cn'
        }
      },
      '/rl-bigpixel': {
        target: 'https://rl.bigpixel.cn',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/rl-bigpixel/, ''),
        headers: {
          'Referer': 'https://rl.bigpixel.cn',
          'Origin': 'https://rl.bigpixel.cn'
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 使用单一入口文件，简化配置
        additionalData: `@import "@/styles/index.scss";`,
        // 完全静默化Sass警告
        quietDeps: true,
        silenceDeprecations: ['legacy-js-api', 'import', 'global-builtin', 'mixed-decls', 'color-4-api'],
        logger: {
          warn: () => {},
          debug: () => {}
        }
      }
    }
  },
  build: {
    // 代码分割配置
    rollupOptions: {
      output: {
        manualChunks: {
          // 将Vue相关库打包到vendor chunk
          vendor: ['vue', 'vue-router', 'pinia'],
          // 将UI库打包到ui chunk
          ui: ['vant']
        }
      }
    },
    // 设置较大的chunk大小警告阈值
    chunkSizeWarningLimit: 1000,
    // 启用源码映射（开发时有用）
    sourcemap: false
  }
}) 