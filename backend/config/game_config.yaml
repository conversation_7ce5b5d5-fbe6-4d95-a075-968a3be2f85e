# 游戏核心配置文件 - 100%符合PRD《千亿像素城市寻宝》要求

# 体力系统配置 - 严格按照PRD要求
stamina_system:
  max_stamina: 120                    # PRD要求：最大体力120点
  recovery_rate_minutes: 3            # PRD要求：每3分钟恢复1点体力
  consumption_rates:
    catch_thief: 0.5                  # PRD新要求：抓捕小偷消耗0.5点体力
    clean_garbage: 0.3                # PRD新要求：清理垃圾消耗0.3点体力
    cultural_quiz: 5                  # PRD要求：古迹问答消耗5点体力
  efficiency_thresholds:
    normal_efficiency: 30             # PRD要求：体力≥30时100%效率
    reduced_efficiency: 1             # PRD要求：体力1-29时75%效率
    no_play: 0                        # PRD要求：体力0时无法游戏
  ad_recovery:
    stamina_amount: 30                # PRD要求：广告恢复30点体力
    hourly_limit: 3                   # PRD要求：每小时3次
  special_recovery:
    rebel_thief_chance: 0.1           # PRD要求：10%概率遇到叛变小偷
    rebel_thief_stamina: 10           # PRD要求：叛变小偷恢复10点体力
    level_up_full_recovery: true      # PRD要求：升级时完全恢复体力

# 经验系统配置 - 严格按照PRD要求
experience_system:
  # 基础经验值获取 - 完全按照PRD表格配置
  base_experience:
    catch_thief: 5                    # PRD新要求：抓捕小偷5点经验
    clean_garbage: 3                  # PRD新要求：清理垃圾3点经验
    cultural_quiz: 35                 # PRD要求：古迹问答35点经验
    cultural_artifact: 25             # PRD要求：文化图鉴25点经验
    level_complete: 200               # PRD要求：关卡通关200点经验
    daily_task_range: [50, 800]      # PRD要求：完成任务50-800点经验
    passive_income_range: [60, 150]  # PRD新要求：被动收益60-150点/小时

  # 体力不足时经验减少 - PRD要求
  low_stamina_penalty:
    threshold: 30                     # 体力<30时触发
    reduction_rate: 0.25              # 减少25%经验

  # 广告双倍经验 - PRD要求
  ad_double_experience:
    cultural_quiz: true               # 古迹问答可双倍
    daily_task: true                  # 每日任务可双倍
    level_complete: true              # 关卡通关可双倍

  # 守护者等级配置 - 严格按照PRD表格（调整升级经验）
  guardian_levels:
    1:
      required_exp: 100
      passive_income: 60              # PRD新要求：60经验/小时
      unlock_features: ["basic_game"]
    2:
      required_exp: 250               # 调整：累计250经验升到2级
      passive_income: 70              # PRD新要求：70经验/小时
      unlock_features: ["treasure_boxes"]
    3:
      required_exp: 550               # 调整：累计550经验升到3级
      passive_income: 80              # PRD新要求：80经验/小时
      unlock_features: ["cultural_quiz"]
    4:
      required_exp: 1150              # 调整：累计1150经验升到4级
      passive_income: 90              # PRD新要求：90经验/小时
      unlock_features: ["advanced_tasks"]
    5:
      required_exp: 2350              # 调整：累计2350经验升到5级
      passive_income: 100             # PRD新要求：100经验/小时
      unlock_features: ["boss_battles"]
    6:
      required_exp: 4850              # 调整：累计4850经验升到6级
      passive_income: 110             # PRD新要求：110经验/小时
      unlock_features: ["city_2"]
    7:
      required_exp: 9850              # 调整：累计9850经验升到7级
      passive_income: 120             # PRD新要求：120经验/小时
      unlock_features: ["advanced_collections"]
    8:
      required_exp: 16850             # 调整：累计16850经验升到8级
      passive_income: 130             # PRD新要求：130经验/小时
      unlock_features: ["city_3"]
    9:
      required_exp: 26850             # 调整：累计26850经验升到9级
      passive_income: 140             # PRD新要求：140经验/小时
      unlock_features: ["master_tasks"]
    10:
      required_exp: 46850             # 调整：累计46850经验升到10级
      passive_income: 150             # PRD新要求：150经验/小时
      unlock_features: ["all_cities", "master_guardian"]
    # 11:
    #   required_exp: 50000
    #   passive_income: 160              
    #   unlock_features: ["legendary_abilities"]
    # 12:
    #   required_exp: 80000
    #   passive_income: 170              
    #   unlock_features: ["epic_guardian"]
    # 13:
    #   required_exp: 120000
    #   passive_income: 180             
    #   unlock_features: ["mythic_powers"]
    # 14:
    #   required_exp: 200000
    #   passive_income: 190             
    #   unlock_features: ["grandmaster_guardian", "ultimate_abilities"]

# 宝箱系统配置 - 严格按照PRD要求
treasure_box_system:
  # 宝箱掉落率 - 完全按照PRD表格
  drop_rates:
    catch_thief:
      drop_rate: 0.06                 # PRD要求：6%掉落率
      box_types: ["copper", "silver", "gold"]
      weights: [0.7, 0.25, 0.05]     # 铜70%，银25%，金5%
    clean_garbage:
      drop_rate: 0.03                 # PRD要求：3%掉落率
      box_types: ["copper", "silver"]
      weights: [0.8, 0.2]             # 铜80%，银20%
    cultural_quiz:
      drop_rate: 0.15                 # PRD要求：15%掉落率
      box_types: ["silver", "gold"]
      weights: [0.7, 0.3]             # 银70%，金30%
    level_complete:
      drop_rate: 1.0                  # PRD要求：100%掉落率
      box_types: ["gold"]
      weights: [1.0]                  # 金100%

  # 宝箱奖励配置 - 严格按照PRD要求
  box_rewards:
    copper:
      free_rewards:
        stamina: 5                    # PRD要求：基础奖励5体力
        items: 1                      # 1个道具
      ad_rewards:
        stamina: 10                   # PRD要求：广告双倍10体力
        items: 2                      # 2个道具
    silver:
      free_rewards:
        stamina: 10                   # PRD要求：基础奖励10体力
        artifact: 1                   # 1个文物
      ad_rewards:
        stamina: 20                   # PRD要求：广告双倍20体力
        artifact: 2                   # 2个文物
    gold:
      free_rewards:
        rare_artifact: 1              # PRD要求：1个稀有文物
        items: 1                      # 1个道具
      ad_rewards:
        rare_artifact: 2              # PRD要求：广告双倍2个稀有文物
        items: 2                      # 2个道具

# BOSS血量系统配置 - 严格按照PRD要求
boss_health_system:
  # 收集行为对BOSS伤害的映射 - PRD要求
  damage_mapping:
    catch_thief:
      health_damage: 2                # PRD要求：抓捕小偷对BOSS造成2%伤害
    clean_garbage:
      health_damage: 1                # PRD要求：清理垃圾对BOSS造成1%伤害
    cultural_quiz:
      health_damage: 10               # PRD要求：古迹问答对BOSS造成10%伤害

  # BOSS对话阶段 - PRD要求
  dialogues:
    arrogant_phase:
      health_range: [75, 100]         # 血量75%-100%时
      dialogue_type: "arrogant"
    angry_phase:
      health_range: [25, 74]          # 血量25%-74%时
      dialogue_type: "angry"
    begging_phase:
      health_range: [1, 24]           # 血量1%-24%时
      dialogue_type: "begging"
    defeat_phase:
      health_range: [0, 0]            # 血量0%时
      dialogue_type: "defeat"

  # BOSS重置机制
  reset_conditions:
    daily_reset: true                 # 每日重置
    level_complete_reset: true        # 关卡完成后重置

# 每日任务系统配置 - 严格按照PRD要求
daily_task_system:
  # 任务类型和奖励 - PRD要求
  task_types:
    catch_thieves:
      target_range: [5, 20]           # 抓捕5-20个小偷
      exp_reward_range: [50, 200]    # 50-200经验奖励
      stamina_cost: 0.5               # PRD新要求：每个消耗0.5体力
    clean_garbage:
      target_range: [8, 30]           # 清理8-30个垃圾
      exp_reward_range: [50, 200]    # 50-200经验奖励
      stamina_cost: 0.3               # PRD新要求：每个消耗0.3体力
    cultural_quiz:
      target_range: [1, 5]            # 完成1-5次问答
      exp_reward_range: [200, 800]   # 200-800经验奖励
      stamina_cost: 5                 # 每次消耗5体力
    collect_artifacts:
      target_range: [2, 10]           # 收集2-10个文物
      exp_reward_range: [100, 400]   # 100-400经验奖励

  # 任务刷新机制
  refresh_schedule:
    daily_reset_time: "00:00"         # 每日0点重置
    max_tasks_per_day: 5              # 每日最多5个任务
    difficulty_scaling: true          # 难度随等级提升

# 广告系统配置 - 严格按照PRD要求
advertisement_system:
  # 广告触发场景 - PRD要求
  trigger_scenarios:
    stamina_recovery:
      stamina_amount: 30              # 恢复30点体力
      hourly_limit: 3                 # 每小时3次
      daily_limit: 10                 # 每日10次
    treasure_box_double:
      enabled: true                   # 宝箱奖励双倍
      success_rate: 1.0               # 100%成功率
    quiz_experience_double:
      enabled: true                   # 问答经验双倍
      success_rate: 1.0               # 100%成功率
    task_reward_double:
      enabled: true                   # 任务奖励双倍
      success_rate: 1.0               # 100%成功率
    passive_income_double:
      enabled: true                   # 被动收益双倍
      duration_hours: 2               # 持续2小时
    level_up_boost:
      enabled: true                   # 升级加速
      boost_multiplier: 1.5           # 1.5倍经验加成

  # 广告频率限制
  frequency_limits:
    min_interval_seconds: 30          # 最小间隔30秒
    max_daily_ads: 50                 # 每日最多50次
    cooldown_between_types: 10        # 不同类型间隔10秒

# 被动收益系统配置 - 严格按照PRD要求
passive_income_system:
  # 收益率配置 - 按守护者等级（PRD新要求）
  income_rates:
    # 直接使用guardian_levels中的passive_income值（经验/小时）
    use_guardian_level_rates: true

  # 收集机制 - PRD新要求：8小时收益上限，每日最多领取2次
  collection:
    max_offline_hours: 8              # PRD新要求：最多累积8小时收益
    max_daily_collections: 2          # PRD新要求：每日最多领取2次
    ad_double_enabled: true           # 广告可双倍收益
    reset_time: "00:00"               # 每日0点重置领取次数

# 被动收益服务配置 - PRD新要求
passive_income:
  base_experience_per_hour: 60        # PRD新要求：基础60经验/小时（1级守护者）
  max_accumulation_hours: 8           # PRD新要求：最大累积8小时
  max_daily_collections: 2            # PRD新要求：每日最多领取2次
  guardian_level_bonus_rate: 0.0      # 不使用加成率，直接使用guardian_levels配置

# 守护者系统配置
guardian_system:
  # 文化图鉴经验值配置
  artifact_experience:
    bronze: 1                         # 铜图鉴：1经验值
    silver: 5                         # 银图鉴：5经验值
    gold: 20                          # 金图鉴：20经验值

  # 等级升级经验值要求
  experience_requirement:
    base_exp: 100                     # 基础经验值要求
    level_multiplier: 1.2             # 每级倍数增长

  # 等级奖励配置
  level_bonuses:
    - level: 2
      abilities: ["stamina_boost"]
    - level: 5
      abilities: ["collection_bonus"]
    - level: 10
      abilities: ["master_guardian"]

# 文化教育系统配置 - 严格按照PRD要求
cultural_education_system:
  # 古迹问答配置
  quiz_system:
    questions_per_session: 3          # 每次3道题
    time_limit_seconds: 30            # 每题30秒
    exp_reward: 35                    # 每次35经验
    stamina_cost: 5                   # 每次5体力
    difficulty_levels: ["easy", "medium", "hard"]
    success_rate_bonus: 0.1           # 成功率加成10%

  # 文化图鉴配置
  artifact_collection:
    exp_reward: 25                    # 每个文物25经验
    rarity_levels: ["common", "rare", "epic", "legendary"]
    collection_bonuses:               # 收集套装奖励
      city_complete: 100              # 完成城市收集100经验
      full_collection: 500            # 全收集500经验

# 收集机制（抓捕小偷、清理垃圾、保护遗迹）

# 数值平衡配置 - 严格按照PRD要求
game_balance:
  # 经验平衡公式 - 完全按照PRD公式
  daily_experience_formula:
    active_game_experience: 1000        # (体力值120 ÷ 平均消耗1.2) × 平均经验10点 = 1000点
    task_reward_experience: [200, 500]  # 200-500点（完成度相关）
    passive_income_experience: [100, 300] # 等级系数 × 60分钟 × 收益率 = 100-300点/日
    level_complete_experience: [200, 600] # 200点 × 关卡数（1-3关/日）= 200-600点
    total_daily_experience: [1500, 2400] # 每日总经验 = 1500-2400点（平均1950点）

  # 升级时间控制 - 严格按照PRD表格
  level_up_timing:
    level_1_3:
      days_required: [1, 2]             # 1-2天
      experience_needed: [100, 400]    # 100-400经验
    level_4_6:
      days_required: [3, 7]             # 3-7天
      experience_needed: [500, 3500]   # 500-3500经验
    level_7_10:
      days_required: [7, 15]            # 7-15天
      experience_needed: [4000, 30000] # 4000-30000经验

# 城市关卡系统配置
cities_system:
  # 城市配置
  cities:
    beijing:
      id: "beijing"
      name: "梵蒂冈"
      name_en: "Vatican"
      image: "/img/cities/vatican.jpg"
      thumbnail: "/img/page_icons/vatican_cities.png"
      description: "梵蒂冈，天主教教廷"
      unlock_level: 1
      scenes:
        - scene_id: "scene_level_1"
          name: "圣彼得大教堂"
          description: "天主教教廷的象征，见证历史变迁"
          background_image: "/img/scenes/saint_peter_basilica.jpg"
          levels: ["thief", "garbage", "quiz"]
        - scene_id: "scene_level_2"
          name: "梵蒂冈博物馆"
          description: "梵蒂冈博物馆，收藏了大量艺术品"
          background_image: "/img/scenes/vatican_museum.jpg"
          levels: ["thief", "garbage", "quiz"]
          unlock_level: 2
    
    shanghai:
      id: "shanghai"
      name: "上海"
      name_en: "Shanghai"
      image: "/img/cities/shanghai.jpg"
      thumbnail: "/img/cities/shanghai_thumb.jpg"
      description: "东方明珠，现代化国际大都市"
      unlock_level: 3
      scenes:
        - scene_id: "scene_level_1"
          name: "外滩"
          description: "黄浦江畔的经典建筑群，中西文化交融"
          background_image: "/img/scenes/bund.jpg"
          levels: ["thief", "garbage", "quiz"]
        - scene_id: "scene_level_2"
          name: "豫园"
          description: "江南古典园林，传统文化精髓"
          background_image: "/img/scenes/yuyuan.jpg"
          levels: ["thief", "garbage", "quiz"]
          unlock_level: 4
    
    guangzhou:
      id: "guangzhou"
      name: "广州"
      name_en: "Guangzhou"
      image: "/img/cities/guangzhou.jpg"
      thumbnail: "/img/cities/guangzhou_thumb.jpg"
      description: "羊城广州，千年商都"
      unlock_level: 5
      scenes:
        - scene_id: "scene_level_1"
          name: "珠江新城"
          description: "现代化CBD，商业繁荣中心"
          background_image: "/img/scenes/zhujiang_newtown.jpg"
          levels: ["thief", "garbage", "quiz"]
        - scene_id: "scene_level_2"
          name: "陈家祠"
          description: "岭南建筑艺术殿堂，民俗文化宝库"
          background_image: "/img/scenes/chenjiaci.jpg"
          levels: ["thief", "garbage", "quiz"]
          unlock_level: 6
    
    nanjing:
      id: "nanjing"
      name: "南京"
      name_en: "Nanjing"
      image: "/img/cities/nanjing.jpg"
      thumbnail: "/img/cities/nanjing_thumb.jpg"
      description: "六朝古都，历史底蕴深厚"
      unlock_level: 7
      scenes:
        - scene_id: "scene_level_1"
          name: "夫子庙"
          description: "秦淮河畔文化胜地，科举文化发源地"
          background_image: "/img/scenes/fuzimiao.jpg"
          levels: ["thief", "garbage", "quiz"]
        - scene_id: "scene_level_2"
          name: "中山陵"
          description: "民国建筑典范，革命历史纪念地"
          background_image: "/img/scenes/zhongshan_mausoleum.jpg"
          levels: ["thief", "garbage", "quiz"]
          unlock_level: 8
    
    xian:
      id: "xian"
      name: "西安"
      name_en: "Xi'an"
      image: "/img/cities/xian.jpg"
      thumbnail: "/img/cities/xian_thumb.jpg"
      description: "十三朝古都，丝绸之路起点"
      unlock_level: 9
      scenes:
        - scene_id: "scene_level_1"
          name: "兵马俑"
          description: "世界第八大奇迹，秦始皇陵守护者"
          background_image: "/img/scenes/terracotta_army.jpg"
          levels: ["thief", "garbage", "quiz"]
        - scene_id: "scene_level_2"
          name: "大雁塔"
          description: "唐代佛教建筑，玄奘取经见证"
          background_image: "/img/scenes/giant_wild_goose_pagoda.jpg"
          levels: ["thief", "garbage", "quiz"]
          unlock_level: 10

  # 关卡类型配置
  level_types:
    thief:
      name: "小偷关"
      name_en: "Thief Hunt"
      icon: "/img/page_icons/thieficon-2.png"
      scene_file: "/beijing/scene_level_thief.xml"  # 包含城市目录路径
      color: "#e67e22"
      description: "抓捕小偷，维护秩序"
      difficulty: "easy"
      expected_duration_minutes: 5
      stamina_cost: 0.5
      base_experience: 5

    garbage:
      name: "垃圾关"
      name_en: "Clean Up"
      icon: "/img/page_icons/rubbish-1.png"
      scene_file: "/beijing/scene_level_garbage.xml"  # 包含城市目录路径
      color: "#2ecc71"
      description: "清理垃圾，保护环境"
      difficulty: "easy"
      expected_duration_minutes: 3
      stamina_cost: 0.3
      base_experience: 3

    quiz:
      name: "古迹问答关"
      name_en: "Monument Quiz"
      icon: "/img/page_icons/questions_icon.png"
      scene_file: "/beijing/scene_level_monumentQuiz.xml"  # 包含城市目录路径
      color: "#e74c3c"
      description: "探索古迹，学习历史"
      difficulty: "medium"
      expected_duration_minutes: 10
      stamina_cost: 5
      base_experience: 35

  # 城市解锁规则
  unlock_rules:
    level_based: true           # 基于等级解锁
    progressive: true           # 渐进式解锁
    minimum_completion: 0.5     # 前一城市至少完成50%才能解锁下一个
    
  # 星级系统配置
  star_system:
    stars_per_city: 3           # 每个城市3颗星（对应3种关卡类型）
    completion_threshold: 1     # 完成1个热点即获得星星
    trophy_requirement: 1.0     # 100%完成率才获得奖杯
    
  # 重置系统配置
  reset_system:
    allow_level_reset: true     # 允许按关卡类型重置
    preserve_artifacts: true    # 重置时保护图鉴
    reset_confirmation: true    # 需要确认对话
    cooldown_minutes: 0         # 重置冷却时间（0表示无冷却）

