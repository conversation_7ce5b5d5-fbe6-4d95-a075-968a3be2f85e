"""
热点数据格式验证器
标准化热点数据的验证逻辑，确保数据格式一致性
"""
from typing import Dict, Any, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class HotspotValidator:
    """热点数据验证器 - 统一验证热点数据格式"""
    
    # 支持的热点类型
    VALID_HOTSPOT_TYPES = ["thief", "garbage", "treasure", "boss_thief", "quiz"]
    
    # 支持的生成模式
    VALID_GENERATION_MODES = ["preset", "mixed", "random"]
    
    # 支持的奖励类型
    VALID_REWARD_TYPES = ["experience", "artifact", "treasure_box"]
    
    @classmethod
    def validate_hotspot_json(cls, json_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        验证热点JSON数据格式
        
        Args:
            json_data: 热点JSON数据
            
        Returns:
            (is_valid, error_message)
        """
        try:
            # 检查必需字段
            if "generation_mode" not in json_data:
                return False, "缺少必需字段: generation_mode"
            
            # 验证生成模式
            generation_mode = json_data["generation_mode"]
            if generation_mode not in cls.VALID_GENERATION_MODES:
                return False, f"无效的生成模式: {generation_mode}，支持的模式: {cls.VALID_GENERATION_MODES}"
            
            # 如果是preset或mixed模式，需要验证hotspots数据
            if generation_mode in ["preset", "mixed"]:
                hotspots = json_data.get("hotspots", [])
                if not isinstance(hotspots, list):
                    return False, "hotspots字段必须是数组类型"
                
                for i, hotspot in enumerate(hotspots):
                    is_valid, error = cls.validate_hotspot_data(hotspot)
                    if not is_valid:
                        return False, f"热点数据[{i}]验证失败: {error}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"验证热点JSON数据时发生异常: {e}")
            return False, f"验证异常: {str(e)}"
    
    @classmethod
    def validate_hotspot_data(cls, hotspot: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        验证单个热点数据格式
        
        Args:
            hotspot: 热点数据
            
        Returns:
            (is_valid, error_message)
        """
        try:
            # 检查必需字段
            required_fields = ["name", "type"]
            for field in required_fields:
                if field not in hotspot:
                    return False, f"缺少必需字段: {field}"
            
            # 验证热点名称
            name = hotspot["name"]
            if not isinstance(name, str) or not name.strip():
                return False, "热点名称必须是非空字符串"
            
            # 验证热点类型
            hotspot_type = hotspot["type"]
            if hotspot_type not in cls.VALID_HOTSPOT_TYPES:
                return False, f"无效的热点类型: {hotspot_type}，支持的类型: {cls.VALID_HOTSPOT_TYPES}"
            
            # 验证位置数据（支持两种格式：position 或 ath/atv）
            position_valid, position_error = cls._validate_position_data(hotspot)
            if not position_valid:
                return False, f"位置数据无效: {position_error}"
            
            # 验证奖励数据（如果存在）
            if "reward" in hotspot:
                reward_valid, reward_error = cls._validate_reward(hotspot["reward"])
                if not reward_valid:
                    return False, f"奖励数据无效: {reward_error}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"验证热点数据时发生异常: {e}")
            return False, f"验证异常: {str(e)}"
    
    @classmethod
    def _validate_position_data(cls, hotspot: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证位置数据（支持两种格式）"""
        # 格式1：position 对象（包含 x, y 坐标）
        if "position" in hotspot:
            return cls._validate_position(hotspot["position"])
        
        # 格式2：krpano 格式（ath, atv 坐标）
        elif "ath" in hotspot and "atv" in hotspot:
            return cls._validate_krpano_position(hotspot)
        
        else:
            return False, "缺少位置数据，需要 position 对象或 ath/atv 坐标"
    
    @classmethod
    def _validate_position(cls, position: Any) -> Tuple[bool, Optional[str]]:
        """验证标准位置数据（x, y坐标）"""
        if not isinstance(position, dict):
            return False, "位置数据必须是对象类型"
        
        # 检查坐标字段
        if "x" not in position or "y" not in position:
            return False, "位置数据必须包含x和y坐标"
        
        try:
            x, y = float(position["x"]), float(position["y"])
            
            # 验证坐标范围（0-1之间的相对坐标）
            if not (0 <= x <= 1) or not (0 <= y <= 1):
                return False, "坐标值必须在0-1之间"
                
        except (ValueError, TypeError):
            return False, "坐标值必须是有效的数字"
        
        return True, None
    
    @classmethod
    def _validate_krpano_position(cls, hotspot: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证krpano位置数据（ath, atv坐标）"""
        try:
            ath = float(hotspot["ath"])
            atv = float(hotspot["atv"])
            
            # 验证方位角范围（-180 到 180 度）
            if not (-180 <= ath <= 180):
                return False, "方位角(ath)必须在-180到180度之间"
            
            # 验证仰角范围（-90 到 90 度）
            if not (-90 <= atv <= 90):
                return False, "仰角(atv)必须在-90到90度之间"
                
        except (ValueError, TypeError):
            return False, "ath和atv坐标值必须是有效的数字"
        
        return True, None
    
    @classmethod
    def _validate_reward(cls, reward: Any) -> Tuple[bool, Optional[str]]:
        """验证奖励数据"""
        if not isinstance(reward, dict):
            return False, "奖励数据必须是对象类型"
        
        # 检查必需字段
        if "type" not in reward:
            return False, "奖励数据必须包含type字段"
        
        # 验证奖励类型
        reward_type = reward["type"]
        if reward_type not in cls.VALID_REWARD_TYPES:
            return False, f"无效的奖励类型: {reward_type}，支持的类型: {cls.VALID_REWARD_TYPES}"
        
        # 验证奖励数量（如果存在）
        if "amount" in reward:
            try:
                amount = int(reward["amount"])
                if amount < 0:
                    return False, "奖励数量不能为负数"
            except (ValueError, TypeError):
                return False, "奖励数量必须是有效的整数"
        
        # 验证宝箱内容（如果是宝箱类型）
        if reward_type == "treasure_box" and "contents" in reward:
            contents = reward["contents"]
            if not isinstance(contents, dict):
                return False, "宝箱内容必须是对象类型"
            
            for resource_type, amount in contents.items():
                if not isinstance(resource_type, str):
                    return False, "资源类型必须是字符串"
                try:
                    amount_val = int(amount)
                    if amount_val < 0:
                        return False, f"资源数量不能为负数: {resource_type}"
                except (ValueError, TypeError):
                    return False, f"资源数量必须是有效的整数: {resource_type}"
        
        return True, None
    
    @classmethod
    def validate_hotspots_data_list(cls, hotspots_data: List[Dict[str, Any]]) -> Tuple[bool, Optional[str]]:
        """
        验证热点数据列表
        
        Args:
            hotspots_data: 热点数据列表
            
        Returns:
            (is_valid, error_message)
        """
        if not isinstance(hotspots_data, list):
            return False, "热点数据必须是数组类型"
        
        for i, hotspot in enumerate(hotspots_data):
            is_valid, error = cls.validate_hotspot_data(hotspot)
            if not is_valid:
                return False, f"热点数据[{i}]验证失败: {error}"
        
        return True, None
    
    @classmethod
    def sanitize_hotspot_data(cls, hotspot: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理和标准化热点数据
        
        Args:
            hotspot: 原始热点数据
            
        Returns:
            清理后的热点数据
        """
        sanitized = {}
        
        # 复制基本字段
        if "name" in hotspot:
            sanitized["name"] = str(hotspot["name"]).strip()
        
        if "type" in hotspot and hotspot["type"] in cls.VALID_HOTSPOT_TYPES:
            sanitized["type"] = hotspot["type"]
        
        # 清理位置数据（支持两种格式）
        if "position" in hotspot and isinstance(hotspot["position"], dict):
            position = hotspot["position"]
            try:
                sanitized["position"] = {
                    "x": max(0, min(1, float(position.get("x", 0)))),
                    "y": max(0, min(1, float(position.get("y", 0))))
                }
            except (ValueError, TypeError):
                sanitized["position"] = {"x": 0.5, "y": 0.5}  # 默认中心位置
        
        # 处理krpano格式位置数据
        elif "ath" in hotspot and "atv" in hotspot:
            try:
                sanitized["ath"] = max(-180, min(180, float(hotspot["ath"])))
                sanitized["atv"] = max(-90, min(90, float(hotspot["atv"])))
            except (ValueError, TypeError):
                sanitized["ath"] = 0.0  # 默认方位角
                sanitized["atv"] = 0.0  # 默认仰角
        
        # 清理奖励数据
        if "reward" in hotspot and isinstance(hotspot["reward"], dict):
            reward = hotspot["reward"]
            if reward.get("type") in cls.VALID_REWARD_TYPES:
                sanitized_reward = {"type": reward["type"]}
                
                if "amount" in reward:
                    try:
                        sanitized_reward["amount"] = max(0, int(reward["amount"]))
                    except (ValueError, TypeError):
                        sanitized_reward["amount"] = 0
                
                # 处理宝箱内容
                if reward["type"] == "treasure_box" and "contents" in reward:
                    contents = reward["contents"]
                    if isinstance(contents, dict):
                        sanitized_contents = {}
                        for res_type, amount in contents.items():
                            try:
                                sanitized_contents[str(res_type)] = max(0, int(amount))
                            except (ValueError, TypeError):
                                continue
                        if sanitized_contents:
                            sanitized_reward["contents"] = sanitized_contents
                
                sanitized["reward"] = sanitized_reward
        
        return sanitized


# 全局验证器实例
hotspot_validator = HotspotValidator()