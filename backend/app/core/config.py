"""
配置管理模块
使用 Pydantic Settings 进行配置管理，支持环境变量和动态配置
"""
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import AnyUrl, field_validator, Field
import yaml
from pathlib import Path


class Settings(BaseSettings):
    """应用配置"""

    # 基础配置
    DEBUG: bool = Field(default=True, env="DEBUG")
    SECRET_KEY: str = Field(default="your-secret-key-change-this-in-production", env="SECRET_KEY")

    # CrazyGames测试配置
    CRAZYGAMES_TEST_MODE: bool = Field(default=False, env="CRAZYGAMES_TEST_MODE")
    CRAZYGAMES_TEST_PUBLIC_KEY: str = Field(default="", env="CRAZYGAMES_TEST_PUBLIC_KEY")
    API_V1_STR: str = Field(default="/api/v1", env="API_V1_STR")
    PROJECT_NAME: str = Field(default="城市全景寻物游戏", env="PROJECT_NAME")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")  # development, staging, production

    # 数据库配置
    DATABASE_URL: str = Field(default="mysql+aiomysql://root:your_password@localhost:3306/universe_vr_game", env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=10, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")  # 生产环境关闭SQL日志

    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    REDIS_DECODE_RESPONSES: bool = Field(default=True, env="REDIS_DECODE_RESPONSES")
    REDIS_EXPIRE_TIME: int = Field(default=3600, env="REDIS_EXPIRE_TIME")  # 默认过期时间(秒)

    # JWT配置
    JWT_SECRET_KEY: str = Field(default="your-jwt-secret-key-change-this-in-production", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="JWT_REFRESH_TOKEN_EXPIRE_DAYS")

    # 第三方登录配置
    GOOGLE_CLIENT_ID: str = Field(default="your-google-client-id", env="GOOGLE_CLIENT_ID")

    # 游戏配置 (核心配置，支持环境变量覆盖)
    MAX_DAILY_ADS: int = Field(default=5, env="MAX_DAILY_ADS")
    MAX_SESSION_DURATION: int = Field(default=3600, env="MAX_SESSION_DURATION")  # 最大会话时长(秒)
    HOTSPOT_CACHE_TTL: int = Field(default=300, env="HOTSPOT_CACHE_TTL")  # 热点缓存时间(秒)

    # 安全配置
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    MAX_LOGIN_ATTEMPTS: int = Field(default=5, env="MAX_LOGIN_ATTEMPTS")
    CORS_ORIGINS: list = Field(default=["*"], env="CORS_ORIGINS")  # 生产环境需要限制

    # 数据分析与监控
    ENABLE_ANALYTICS: bool = Field(default=True, env="ENABLE_ANALYTICS")
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")
    METRICS_PREFIX: str = Field(default="universe_vr_game", env="METRICS_PREFIX")
    HEALTH_CHECK_INTERVAL: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")  # 健康检查间隔(秒)

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="logs/app.log", env="LOG_FILE")
    LOG_ROTATION: str = Field(default="1 day", env="LOG_ROTATION")
    LOG_RETENTION: str = Field(default="30 days", env="LOG_RETENTION")
    LOG_FORMAT: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")

    # AI服务配置
    AI_SERVICE_URL: Optional[str] = Field(default=None, env="AI_SERVICE_URL")
    AI_API_KEY: Optional[str] = Field(default=None, env="AI_API_KEY")
    AI_TIMEOUT: int = Field(default=30, env="AI_TIMEOUT")  # AI服务超时时间(秒)
    
    # 动态配置文件路径 (使用绝对路径)
    @property
    def GAME_CONFIG_PATH(self) -> Path:
        return Path(__file__).parent.parent.parent / "config" / "game_config.yaml"

    @property
    def CITIES_CONFIG_PATH(self) -> Path:
        return Path(__file__).parent.parent.parent / "config" / "cities_artifacts.yaml"

    @property
    def HOTSPOT_TEMPLATES_PATH(self) -> Path:
        return Path(__file__).parent.parent.parent / "config" / "hotspot_templates.yaml"

    
    class Config:
        env_file = ".env"
        case_sensitive = True


class GameConfig:
    """游戏配置管理器 - 支持动态加载和更新"""
    
    def __init__(self, config_path: Path):
        self.config_path = config_path
        self._config: Dict[str, Any] = {}
        self._last_modified = 0
        self.reload()
    
    def reload(self) -> None:
        """重新加载配置文件"""
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            self._last_modified = self.config_path.stat().st_mtime
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        # 检查文件是否存在并被修改
        if self.config_path.exists() and self.config_path.stat().st_mtime > self._last_modified:
            self.reload()

        keys = key.split('.')
        value = self._config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值（仅在内存中，不写入文件）"""
        keys = key.split('.')
        config = self._config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
    
    def save(self) -> None:
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, allow_unicode=True, default_flow_style=False)
        self._last_modified = self.config_path.stat().st_mtime


class AnalyticsConfig:
    """数据分析配置"""
    
    # 需要追踪的核心指标
    METRICS = {
        # 用户指标
        "user_registration": "新用户注册数",
        "user_login": "用户登录次数",
        "dau": "日活跃用户数",
        "wau": "周活跃用户数",
        "mau": "月活跃用户数",
        "retention_rate": "留存率",
        
        # 游戏指标
        "session_started": "游戏会话开始数",
        "session_completed": "游戏会话完成数",
        "session_duration": "游戏会话时长",
        "hotspot_collected": "热点收集数",
        "boss_defeated": "BOSS击败数",

        
        # 商业化指标
        "ad_watched": "广告观看次数",
        "ad_revenue": "广告收入",
        "share_created": "分享创建数",
        "share_redeemed": "分享兑换数",
        
        # 收集指标
        "artifact_found": "文物发现数",
        "artifact_duplicate": "重复文物数",
        "collection_rate": "图鉴收集率",
        "city_unlocked": "城市解锁数",
        
        # 性能指标
        "api_latency": "API延迟",
        "api_error_rate": "API错误率",
        "db_connection_pool": "数据库连接池使用率",
        "redis_memory_usage": "Redis内存使用率",
    }
    
    # 数据聚合时间窗口
    AGGREGATION_WINDOWS = {
        "realtime": "实时",
        "hourly": "小时",
        "daily": "天",
        "weekly": "周",
        "monthly": "月"
    }
    
    # 告警阈值
    ALERT_THRESHOLDS = {
        "api_error_rate": {"max": 0.05, "window": "5m"},
        "db_connection_pool": {"max": 0.8, "window": "1m"},
        "redis_memory_usage": {"max": 0.9, "window": "5m"},
        "dau_drop": {"min_ratio": 0.7, "window": "1d"},
    }


# 实例化配置
settings = Settings()

# 动态配置管理器
game_config = GameConfig(settings.GAME_CONFIG_PATH)
cities_config = GameConfig(settings.CITIES_CONFIG_PATH)
hotspot_templates_config = GameConfig(settings.HOTSPOT_TEMPLATES_PATH)

# 数据分析配置
analytics_config = AnalyticsConfig() 