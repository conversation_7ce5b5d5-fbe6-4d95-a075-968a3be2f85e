"""
缓存键命名规范和常量定义
统一管理所有Redis缓存键的命名规范
"""


class CacheKeys:
    """缓存键常量和生成器"""
    
    # 热点相关缓存键
    @staticmethod
    def hotspot_session(session_id: str, hotspot_id: str) -> str:
        """会话内热点状态: hotspot:session:{session_id}:{hotspot_id}"""
        return f"hotspot:session:{session_id}:{hotspot_id}"
    
    @staticmethod
    def user_hotspots(user_id: int, city_id: str = "*", scene_id: str = "*") -> str:
        """用户热点收集状态: hotspot:user:{user_id}:{city_id}:{scene_id}"""
        return f"hotspot:user:{user_id}:{city_id}:{scene_id}"
    
    @staticmethod
    def game_session(session_id: str) -> str:
        """游戏会话: game:session:{session_id}"""
        return f"game:session:{session_id}"
    
    @staticmethod
    def user_progress(user_id: int, city_id: str = "*") -> str:
        """用户城市进度: progress:user:{user_id}:{city_id}"""
        return f"progress:user:{user_id}:{city_id}"
    
    # 通配符模式用于批量操作
    @staticmethod
    def hotspot_session_pattern(session_id: str) -> str:
        """会话内所有热点: hotspot:session:{session_id}:*"""
        return f"hotspot:session:{session_id}:*"
    
    @staticmethod
    def user_hotspots_pattern(user_id: int) -> str:
        """用户所有热点: hotspot:user:{user_id}:*"""
        return f"hotspot:user:{user_id}:*"
    
    @staticmethod
    def all_hotspots_pattern() -> str:
        """所有热点缓存: hotspot:*"""
        return "hotspot:*"
    
    @staticmethod
    def all_sessions_pattern() -> str:
        """所有游戏会话: game:session:*"""
        return "game:session:*"


# 为向后兼容导出一些常用的缓存键生成器
hotspot_session_key = CacheKeys.hotspot_session
user_hotspots_key = CacheKeys.user_hotspots
game_session_key = CacheKeys.game_session