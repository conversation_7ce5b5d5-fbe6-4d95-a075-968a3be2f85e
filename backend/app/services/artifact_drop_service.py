"""
图鉴掉落服务
实现图鉴掉落的保护机制，确保：
1. 图鉴不会重复掉落
2. 每关图鉴数量固定
3. 掉完就没有了
"""
import logging
import random
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload

from app.models.artifact import UserArtifact
from app.models.city_unlock import UserCityProgress
from app.models.cultural_quiz import CulturalArtifact, UserArtifactCollection
from app.services.config_service import config_service

logger = logging.getLogger(__name__)


class ArtifactDropService:
    """图鉴掉落服务"""
    
    def __init__(self):
        # 每关的图鉴配置（可以通过配置文件或数据库加载）
        self.level_artifact_config = {
            'thief': {
                'max_artifacts': 3,  # 小偷关最多掉落3个图鉴
                'base_drop_rate': 0.9,  # 基础掉落率30%
                'artifact_pool': ['thief_artifact_1', 'thief_artifact_2', 'thief_artifact_3']
            },
            'garbage': {
                'max_artifacts': 2,  # 垃圾关最多掉落2个图鉴
                'base_drop_rate': 0.25,
                'artifact_pool': ['garbage_artifact_1', 'garbage_artifact_2']
            },
            'quiz': {
                'max_artifacts': 5,  # 古迹问答关最多掉落5个图鉴
                'base_drop_rate': 0.4,
                'artifact_pool': ['quiz_artifact_1', 'quiz_artifact_2', 'quiz_artifact_3', 'quiz_artifact_4', 'quiz_artifact_5']
            }
        }
    
    async def attempt_artifact_drop(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str,
        level_type: str,
        hotspot_id: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        尝试掉落图鉴
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            city_id: 城市ID
            level_type: 关卡类型 (thief, garbage, quiz)
            hotspot_id: 热点ID（可选，用于日志）
        
        Returns:
            掉落结果，如果没有掉落则返回None
        """
        try:
            # 检查关卡配置
            if level_type not in self.level_artifact_config:
                logger.warning(f"未配置的关卡类型: {level_type}")
                return None
            
            config = self.level_artifact_config[level_type]
            
            # 获取用户已获得的图鉴
            user_artifacts = await self._get_user_level_artifacts(db, user_id, city_id, level_type)
            
            # 检查是否还有可掉落的图鉴
            obtained_artifact_ids = {artifact.artifact_id for artifact in user_artifacts}
            available_artifacts = [
                artifact_id for artifact_id in config['artifact_pool'] 
                if artifact_id not in obtained_artifact_ids
            ]
            
            if not available_artifacts:
                logger.info(f"用户 {user_id} 在 {level_type} 关卡已获得所有图鉴，无法再掉落")
                return None
            
            # 检查掉落数量限制
            if len(user_artifacts) >= config['max_artifacts']:
                logger.info(f"用户 {user_id} 在 {level_type} 关卡已达到最大图鉴数量 {config['max_artifacts']}")
                return None
            
            # 计算掉落率（已获得图鉴越多，掉落率越低）
            remaining_slots = config['max_artifacts'] - len(user_artifacts)
            adjusted_drop_rate = config['base_drop_rate'] * (remaining_slots / config['max_artifacts'])
            
            # 掉落判定
            if random.random() > adjusted_drop_rate:
                logger.debug(f"图鉴掉落失败，掉落率: {adjusted_drop_rate:.2%}")
                return None
            
            # 随机选择一个可掉落的图鉴
            selected_artifact_id = random.choice(available_artifacts)
            
            # 创建图鉴记录（掉落系统）
            user_artifact = UserArtifact(
                user_id=user_id,
                artifact_id=selected_artifact_id,
                city_id=city_id,
                count=1
            )
            db.add(user_artifact)
            
            # 同时在文化问答系统中创建记录
            await self._sync_to_cultural_quiz_system(db, user_id, selected_artifact_id, city_id)
            
            # 更新城市进度
            await self._update_city_progress(db, user_id, city_id)
            
            await db.commit()
            
            # 获取图鉴详细信息
            artifact_info = await self._get_artifact_info(selected_artifact_id)
            
            logger.info(f"🎁 图鉴掉落成功: user_id={user_id}, artifact_id={selected_artifact_id}, level_type={level_type}")
            
            return {
                "artifact_id": selected_artifact_id,
                "name": artifact_info.get("name", selected_artifact_id),
                "rarity": artifact_info.get("rarity", "common"),
                "level_type": level_type,
                "is_new": True,
                "remaining_artifacts": len(available_artifacts) - 1,
                "total_obtained": len(user_artifacts) + 1,
                "max_artifacts": config['max_artifacts']
            }
            
        except Exception as e:
            logger.error(f"图鉴掉落失败: user_id={user_id}, level_type={level_type}, error={e}")
            await db.rollback()
            return None
    
    async def get_user_level_artifacts_status(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str,
        level_type: str = None
    ) -> Dict[str, Any]:
        """
        获取用户在指定关卡（或所有关卡）的图鉴收集状态
        
        Returns:
            图鉴收集状态信息
        """
        try:
            if level_type:
                # 单个关卡的状态
                config = self.level_artifact_config.get(level_type, {})
                user_artifacts = await self._get_user_level_artifacts(db, user_id, city_id, level_type)
                
                obtained_ids = {artifact.artifact_id for artifact in user_artifacts}
                available_ids = set(config.get('artifact_pool', []))
                remaining_ids = available_ids - obtained_ids
                
                return {
                    "level_type": level_type,
                    "max_artifacts": config.get('max_artifacts', 0),
                    "obtained_count": len(user_artifacts),
                    "remaining_count": len(remaining_ids),
                    "obtained_artifacts": [
                        {
                            "artifact_id": artifact.artifact_id,
                            "count": artifact.count,
                            "first_obtained_at": artifact.first_obtained_at.isoformat() if artifact.first_obtained_at else None
                        }
                        for artifact in user_artifacts
                    ],
                    "remaining_artifacts": list(remaining_ids),
                    "completion_rate": len(user_artifacts) / config.get('max_artifacts', 1) if config.get('max_artifacts', 0) > 0 else 0
                }
            else:
                # 所有关卡的状态
                all_levels_status = {}
                for level_type in self.level_artifact_config.keys():
                    status = await self.get_user_level_artifacts_status(db, user_id, city_id, level_type)
                    all_levels_status[level_type] = status
                
                return {
                    "city_id": city_id,
                    "user_id": user_id,
                    "levels": all_levels_status,
                    "total_obtained": sum(status["obtained_count"] for status in all_levels_status.values()),
                    "total_max": sum(status["max_artifacts"] for status in all_levels_status.values())
                }
                
        except Exception as e:
            logger.error(f"获取图鉴状态失败: user_id={user_id}, level_type={level_type}, error={e}")
            return {"error": "获取图鉴状态失败"}
    
    async def check_artifact_drop_eligibility(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str,
        level_type: str
    ) -> Dict[str, Any]:
        """
        检查图鉴掉落资格
        
        Returns:
            掉落资格信息
        """
        try:
            config = self.level_artifact_config.get(level_type, {})
            user_artifacts = await self._get_user_level_artifacts(db, user_id, city_id, level_type)
            
            obtained_ids = {artifact.artifact_id for artifact in user_artifacts}
            available_ids = set(config.get('artifact_pool', []))
            remaining_ids = available_ids - obtained_ids
            
            can_drop = len(remaining_ids) > 0 and len(user_artifacts) < config.get('max_artifacts', 0)
            
            return {
                "can_drop": can_drop,
                "reason": self._get_drop_ineligibility_reason(user_artifacts, config, remaining_ids),
                "drop_rate": self._calculate_drop_rate(user_artifacts, config) if can_drop else 0,
                "remaining_slots": max(0, config.get('max_artifacts', 0) - len(user_artifacts)),
                "available_artifacts": list(remaining_ids)
            }
            
        except Exception as e:
            logger.error(f"检查图鉴掉落资格失败: {e}")
            return {"can_drop": False, "error": str(e)}
    
    async def _get_user_level_artifacts(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str,
        level_type: str
    ) -> List[UserArtifact]:
        """获取用户在指定关卡的图鉴"""
        config = self.level_artifact_config.get(level_type, {})
        artifact_pool = config.get('artifact_pool', [])
        
        if not artifact_pool:
            return []
        
        query = select(UserArtifact).where(
            and_(
                UserArtifact.user_id == user_id,
                UserArtifact.city_id == city_id,
                UserArtifact.artifact_id.in_(artifact_pool)
            )
        )
        result = await db.execute(query)
        return result.scalars().all()
    
    async def _update_city_progress(self, db: AsyncSession, user_id: int, city_id: str):
        """更新城市进度"""
        try:
            # 获取用户总图鉴数
            result = await db.execute(
                select(func.count(UserArtifact.id)).where(
                    and_(
                        UserArtifact.user_id == user_id,
                        UserArtifact.city_id == city_id
                    )
                )
            )
            total_artifacts = result.scalar() or 0
            
            # 获取或创建城市进度
            progress_result = await db.execute(
                select(UserCityProgress).where(
                    and_(
                        UserCityProgress.user_id == user_id,
                        UserCityProgress.city_id == city_id
                    )
                )
            )
            progress = progress_result.scalar_one_or_none()
            
            if not progress:
                progress = UserCityProgress(
                    user_id=user_id,
                    city_id=city_id,
                    is_unlocked=True,
                    artifacts_collected=total_artifacts,
                    total_artifacts=16  # 总图鉴数，可以配置
                )
                db.add(progress)
            else:
                progress.artifacts_collected = total_artifacts
                progress.collection_rate = int((total_artifacts / progress.total_artifacts) * 100) if progress.total_artifacts > 0 else 0
            
        except Exception as e:
            logger.error(f"更新城市进度失败: {e}")
    
    async def _get_artifact_info(self, artifact_id: str) -> Dict[str, Any]:
        """获取图鉴详细信息"""
        # 这里可以从配置文件或数据库获取图鉴信息
        # 暂时返回基本信息
        return {
            "name": artifact_id.replace("_", " ").title(),
            "rarity": "common" if "artifact_1" in artifact_id else "rare",
            "description": f"来自特定关卡的珍贵图鉴：{artifact_id}"
        }
    
    def _calculate_drop_rate(self, user_artifacts: List[UserArtifact], config: Dict[str, Any]) -> float:
        """计算当前掉落率"""
        remaining_slots = config.get('max_artifacts', 0) - len(user_artifacts)
        if remaining_slots <= 0:
            return 0.0
        
        base_rate = config.get('base_drop_rate', 0.3)
        return base_rate * (remaining_slots / config.get('max_artifacts', 1))
    
    def _get_drop_ineligibility_reason(
        self,
        user_artifacts: List[UserArtifact],
        config: Dict[str, Any],
        remaining_ids: set
    ) -> str:
        """获取无法掉落的原因"""
        if len(user_artifacts) >= config.get('max_artifacts', 0):
            return "已达到最大图鉴数量"
        elif not remaining_ids:
            return "所有图鉴已获得"
        else:
            return "符合掉落条件"
    
    async def _sync_to_cultural_quiz_system(self, db: AsyncSession, user_id: int, artifact_id: str, city_id: str):
        """同步图鉴到文化问答系统"""
        try:
            # 1. 检查或创建 CulturalArtifact 记录
            cultural_artifact_query = select(CulturalArtifact).where(
                CulturalArtifact.artifact_code == artifact_id
            )
            result = await db.execute(cultural_artifact_query)
            cultural_artifact = result.scalar_one_or_none()
            
            if not cultural_artifact:
                # 创建新的文化图鉴记录
                cultural_artifact = CulturalArtifact(
                    artifact_code=artifact_id,
                    city_id=city_id,
                    name=self._get_artifact_display_name(artifact_id),
                    name_en=self._get_artifact_display_name(artifact_id, lang='en'),
                    category="hotspot_collection",
                    rarity=self._get_artifact_rarity(artifact_id),
                    description=f"从{self._get_level_type_name(artifact_id)}关卡获得的珍贵图鉴",
                    cultural_background="通过探索城市热点获得的文化收藏品",
                    historical_significance="记录城市探索历程的重要文物",
                    image_url=f"/img/artifacts/{artifact_id}.png",
                    is_active=True
                )
                db.add(cultural_artifact)
                await db.flush()  # 确保获得ID
            
            # 2. 检查用户是否已收集
            collection_query = select(UserArtifactCollection).where(
                and_(
                    UserArtifactCollection.user_id == user_id,
                    UserArtifactCollection.artifact_id == cultural_artifact.id
                )
            )
            result = await db.execute(collection_query)
            existing_collection = result.scalar_one_or_none()
            
            if not existing_collection:
                # 创建用户收集记录
                user_collection = UserArtifactCollection(
                    user_id=user_id,
                    artifact_id=cultural_artifact.id,
                    obtained_from="hotspot_drop",
                    quantity=1
                )
                db.add(user_collection)
                logger.info(f"✅ 图鉴已同步到文化问答系统: user_id={user_id}, artifact_id={artifact_id}")
            else:
                logger.debug(f"📚 图鉴已存在于文化问答系统: user_id={user_id}, artifact_id={artifact_id}")
                
        except Exception as e:
            logger.error(f"同步图鉴到文化问答系统失败: user_id={user_id}, artifact_id={artifact_id}, error={e}")
            # 不抛出异常，避免影响主流程
    
    def _get_artifact_display_name(self, artifact_id: str, lang: str = 'zh') -> str:
        """获取图鉴显示名称"""
        name_map = {
            'thief_artifact_1': {'zh': '小偷图鉴 I', 'en': 'Thief Artifact I'},
            'thief_artifact_2': {'zh': '小偷图鉴 II', 'en': 'Thief Artifact II'},
            'thief_artifact_3': {'zh': '小偷图鉴 III', 'en': 'Thief Artifact III'},
            'garbage_artifact_1': {'zh': '垃圾图鉴 I', 'en': 'Garbage Artifact I'},
            'garbage_artifact_2': {'zh': '垃圾图鉴 II', 'en': 'Garbage Artifact II'},
            'quiz_artifact_1': {'zh': '古迹图鉴 I', 'en': 'Monument Artifact I'},
            'quiz_artifact_2': {'zh': '古迹图鉴 II', 'en': 'Monument Artifact II'},
            'quiz_artifact_3': {'zh': '古迹图鉴 III', 'en': 'Monument Artifact III'},
            'quiz_artifact_4': {'zh': '古迹图鉴 IV', 'en': 'Monument Artifact IV'},
            'quiz_artifact_5': {'zh': '古迹图鉴 V', 'en': 'Monument Artifact V'},
        }
        return name_map.get(artifact_id, {}).get(lang, artifact_id.replace('_', ' ').title())
    
    def _get_artifact_rarity(self, artifact_id: str) -> str:
        """获取图鉴稀有度"""
        if 'artifact_1' in artifact_id:
            return 'common'
        elif 'artifact_2' in artifact_id:
            return 'rare'
        elif 'artifact_3' in artifact_id:
            return 'epic'
        else:
            return 'rare'
    
    def _get_level_type_name(self, artifact_id: str) -> str:
        """获取关卡类型名称"""
        if 'thief' in artifact_id:
            return '小偷'
        elif 'garbage' in artifact_id:
            return '垃圾'
        elif 'quiz' in artifact_id:
            return '古迹'
        return '未知'
    
    def _get_level_type_from_artifact_id(self, artifact_id: str) -> str:
        """从图鉴ID获取关卡类型"""
        if 'thief' in artifact_id:
            return 'thief'
        elif 'garbage' in artifact_id:
            return 'garbage'
        elif 'quiz' in artifact_id:
            return 'quiz'
        return 'unknown'


# 全局实例
artifact_drop_service = ArtifactDropService()