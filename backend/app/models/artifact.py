"""
文物收集相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Boolean, ForeignKey, UniqueConstraint, Index, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class UserArtifact(Base):
    """用户图鉴收集表"""
    __tablename__ = "user_artifacts"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    artifact_id = Column(String(50), nullable=False, comment="文物ID")
    city_id = Column(String(50), nullable=False, comment="所属城市")
    count = Column(Integer, default=1, comment="拥有数量")
    first_obtained_at = Column(DateTime, server_default=func.now(), comment="首次获得时间")
    last_obtained_at = Column(DateTime, server_default=func.now(), comment="最后获得时间")
    
    
    # 唯一约束和索引
    __table_args__ = (
        UniqueConstraint("user_id", "artifact_id", name="uk_user_artifact"),
        Index("idx_city_id", "city_id"),
        Index("idx_first_obtained", "first_obtained_at"),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            "artifact_id": self.artifact_id,
            "city_id": self.city_id,
            "count": self.count,
            "first_obtained_at": self.first_obtained_at.isoformat() if self.first_obtained_at else None,
            "last_obtained_at": self.last_obtained_at.isoformat() if self.last_obtained_at else None
        }



class CityCollection(Base):
    """城市收藏品表 - 定义每个城市的收藏品项目"""
    __tablename__ = "city_collections"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    city_id = Column(String(50), nullable=False, comment="城市ID")
    item_id = Column(String(100), nullable=False, comment="收藏品ID")
    name = Column(String(200), nullable=False, comment="收藏品名称")
    image_url = Column(String(500), comment="收藏品图片URL")
    rarity = Column(String(50), default="common", comment="稀有度")
    order_index = Column(Integer, default=0, comment="显示顺序")
    description = Column(String(1000), comment="收藏品描述")
    created_at = Column(DateTime, server_default=func.now())

    # 唯一约束和索引
    __table_args__ = (
        UniqueConstraint("city_id", "item_id", name="uk_city_item"),
        Index("idx_city_order", "city_id", "order_index"),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            "item_id": self.item_id,
            "city_id": self.city_id,
            "name": self.name,
            "image_url": self.image_url,
            "rarity": self.rarity,
            "order_index": self.order_index,
            "description": self.description
        }


class UserCollection(Base):
    """用户收藏品表 - 记录用户收集的收藏品"""
    __tablename__ = "user_collections"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    item_id = Column(String(100), nullable=False, comment="收藏品ID")
    collected_at = Column(DateTime, server_default=func.now(), comment="收集时间")

    # 唯一约束和索引
    __table_args__ = (
        UniqueConstraint("user_id", "city_id", "item_id", name="uk_user_collection"),
        Index("idx_user_city", "user_id", "city_id"),
        Index("idx_collected_at", "collected_at"),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "city_id": self.city_id,
            "item_id": self.item_id,
            "collected_at": self.collected_at.isoformat() if self.collected_at else None
        }