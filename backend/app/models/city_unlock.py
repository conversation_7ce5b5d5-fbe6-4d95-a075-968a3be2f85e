"""
城市解锁系统模型
"""
from sqlalchemy import Column, String, Integer, DateTime, Boolean, BigInteger, Text, JSON
from datetime import datetime
import uuid

from app.core.database import Base


class CityInfo(Base):
    """城市信息表"""
    __tablename__ = "city_info"

    id = Column(String(50), primary_key=True, comment="城市ID")
    name = Column(String(100), nullable=False, comment="城市名称")
    name_en = Column(String(100), nullable=True, comment="英文名称")
    country = Column(String(50), nullable=False, comment="所属国家")
    
    # 城市描述
    description = Column(Text, nullable=True, comment="城市描述")
    cultural_significance = Column(Text, nullable=True, comment="文化意义")
    historical_background = Column(Text, nullable=True, comment="历史背景")
    
    # 游戏设置
    unlock_level = Column(Integer, default=1, comment="解锁等级要求")
    unlock_condition = Column(String(100), nullable=True, comment="解锁条件")
    star_requirements = Column(JSON, nullable=True, comment="星级要求配置")
    
    # 关卡配置
    thief_count = Column(Integer, default=0, comment="小偷数量")
    rubbish_count = Column(Integer, default=0, comment="垃圾数量") 
    monument_count = Column(Integer, default=0, comment="古迹数量")
    
    # 媒体资源
    preview_image = Column(String(255), nullable=True, comment="预览图URL")
    panorama_path = Column(String(255), nullable=True, comment="全景图路径")
    background_music = Column(String(255), nullable=True, comment="背景音乐")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    display_order = Column(Integer, default=0, comment="显示顺序")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<CityInfo(id={self.id}, name={self.name}, unlock_level={self.unlock_level})>"


class UserCityProgress(Base):
    """用户城市进度表"""
    __tablename__ = "user_city_progress"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="进度ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    
    # 解锁状态
    is_unlocked = Column(Boolean, default=False, comment="是否已解锁")
    unlocked_at = Column(DateTime, nullable=True, comment="解锁时间")
    unlock_method = Column(String(50), nullable=True, comment="解锁方式")
    
    # 游戏进度
    thief_progress = Column(Integer, default=0, comment="抓捕小偷进度")
    rubbish_progress = Column(Integer, default=0, comment="清理垃圾进度")
    monument_progress = Column(Integer, default=0, comment="古迹修复进度")
    
    # 完成统计
    thief_completed = Column(Integer, default=0, comment="完成的抓捕关卡数")
    rubbish_completed = Column(Integer, default=0, comment="完成的清理关卡数")
    monument_completed = Column(Integer, default=0, comment="完成的古迹关卡数")
    
    # 星级评价
    thief_stars = Column(Integer, default=0, comment="抓捕关卡星级")
    rubbish_stars = Column(Integer, default=0, comment="清理关卡星级")
    monument_stars = Column(Integer, default=0, comment="古迹关卡星级")
    total_stars = Column(Integer, default=0, comment="总星级")
    
    # 收集统计
    artifacts_collected = Column(Integer, default=0, comment="已收集图鉴数")
    total_artifacts = Column(Integer, default=0, comment="图鉴总数")
    collection_rate = Column(Integer, default=0, comment="收集完成度百分比")
    
    # 时间记录
    first_played_at = Column(DateTime, nullable=True, comment="首次游戏时间")
    last_played_at = Column(DateTime, nullable=True, comment="最后游戏时间")
    total_play_time = Column(Integer, default=0, comment="总游戏时间(分钟)")
    
    # 状态
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<UserCityProgress(user_id={self.user_id}, city={self.city_id}, stars={self.total_stars})>"

    def calculate_total_stars(self):
        """计算总星级"""
        self.total_stars = self.thief_stars + self.rubbish_stars + self.monument_stars

    def calculate_collection_rate(self):
        """计算收集完成度"""
        if self.total_artifacts > 0:
            self.collection_rate = int((self.artifacts_collected / self.total_artifacts) * 100)
        else:
            self.collection_rate = 0

    def is_city_completed(self):
        """判断城市是否完全通关"""
        return (self.thief_stars >= 3 and 
                self.rubbish_stars >= 3 and 
                self.monument_stars >= 3 and
                self.collection_rate >= 100)


class CityUnlockCondition(Base):
    """城市解锁条件表"""
    __tablename__ = "city_unlock_conditions"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="条件ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    
    # 解锁条件
    condition_type = Column(String(20), nullable=False, comment="条件类型：level/stars/city_complete/artifact")
    condition_value = Column(Integer, nullable=False, comment="条件数值")
    prerequisite_city = Column(String(50), nullable=True, comment="前置城市ID")
    
    # 条件描述
    condition_text = Column(String(200), nullable=False, comment="条件描述文本")
    condition_text_en = Column(String(200), nullable=True, comment="英文条件描述")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<CityUnlockCondition(city={self.city_id}, type={self.condition_type}, value={self.condition_value})>"


class CityStarRequirement(Base):
    """城市星级要求表"""
    __tablename__ = "city_star_requirements"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="要求ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    level_type = Column(String(20), nullable=False, comment="关卡类型：thief/rubbish/monument")
    
    # 星级要求
    one_star_requirement = Column(JSON, nullable=False, comment="1星要求")
    two_star_requirement = Column(JSON, nullable=False, comment="2星要求")
    three_star_requirement = Column(JSON, nullable=False, comment="3星要求")
    
    # 奖励配置
    one_star_reward = Column(JSON, nullable=True, comment="1星奖励")
    two_star_reward = Column(JSON, nullable=True, comment="2星奖励")
    three_star_reward = Column(JSON, nullable=True, comment="3星奖励")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<CityStarRequirement(city={self.city_id}, level={self.level_type})>"

    def calculate_stars(self, completion_time: int, accuracy: float, bonus_actions: int = 0):
        """计算星级"""
        # 基础星级计算逻辑
        stars = 1  # 基础1星
        
        # 根据完成时间和准确率计算星级
        if accuracy >= 0.9 and completion_time <= 300:  # 5分钟内90%准确率
            stars = 3
        elif accuracy >= 0.8 and completion_time <= 600:  # 10分钟内80%准确率
            stars = 2
            
        # 加分项
        if bonus_actions > 0:
            stars = min(3, stars + 1)
            
        return stars