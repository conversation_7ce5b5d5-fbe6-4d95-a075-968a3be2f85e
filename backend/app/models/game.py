"""
游戏相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Boolean, Enum, ForeignKey, Numeric, JSON, Text, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class SessionStatus(str, enum.Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    COMPLETED = "completed"
    ABANDONED = "abandoned"


class HotspotType(str, enum.Enum):
    """热点类型枚举"""
    THIEF = "thief"
    GARBAGE = "garbage"
    TREASURE = "treasure"
    BOSS_THIEF = "boss_thief"


class RewardType(str, enum.Enum):
    """奖励类型枚举"""
    EXPERIENCE = "experience"
    ARTIFACT = "artifact"


class GameSession(Base):
    """游戏会话表"""
    __tablename__ = "game_sessions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(String(64), unique=True, nullable=False, comment="会话唯一标识")
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    scene_id = Column(String(50), nullable=False, comment="场景ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    status = Column(Enum(SessionStatus), default=SessionStatus.ACTIVE)
    
    
    star = Column(Integer, default=0, comment="星级")
    started_at = Column(DateTime, server_default=func.now())
    ended_at = Column(DateTime)
    duration = Column(Integer, default=0, comment="持续时间(秒)")
    
    
    # 索引
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_scene_id", "scene_id"),
        Index("idx_status", "status"),
        Index("idx_started_at", "started_at"),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "scene_id": self.scene_id,
            "city_id": self.city_id,
            "status": self.status.value if self.status else None,
            "star": self.star,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "duration": self.duration
        }


class HotspotRecord(Base):
    """热点记录表"""
    __tablename__ = "hotspot_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(BigInteger, nullable=False, comment="会话ID，软关联")
    name = Column(String(50), nullable=False, comment="热点名称")
    hotspot_type = Column(Enum(HotspotType), nullable=False)
    position_x = Column(Numeric(10, 6), nullable=False, comment="X坐标")
    position_y = Column(Numeric(10, 6), nullable=False, comment="Y坐标")
    reward_type = Column(Enum(RewardType), nullable=False)
    reward_amount = Column(Integer, default=0)
    artifact_id = Column(String(50), comment="如果是文物奖励，记录文物ID")
    collected = Column(Boolean, default=False)
    collected_at = Column(DateTime)
    created_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # session = relationship("GameSession", back_populates="hotspots")
    
    # 索引
    __table_args__ = (
        Index("idx_session_id", "session_id"),
        Index("idx_hotspot_type", "hotspot_type"),
        Index("idx_collected", "collected"),
    )


# 🚫 PRD合规性清理：移除BossBattle模型
# PRD要求使用BOSS血量系统，不需要主动攻击BOSS的战斗记录
# BOSS血量通过收集行为（抓捕小偷、清理垃圾、古迹问答）自动减少
# 相关功能已在boss_health.py中的BossHealth模型实现


class ShareRecord(Base):
    """分享拉新记录表"""
    __tablename__ = "share_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    sharer_id = Column(BigInteger, nullable=False, comment="分享者ID，软关联")
    invitee_id = Column(BigInteger, comment="被邀请者ID，软关联")
    share_code = Column(String(32), unique=True, nullable=False, comment="分享码")
    city_unlocked = Column(String(50), comment="解锁的城市")
    status = Column(Enum("pending", "completed", "expired", name="share_status"), default="pending")
    created_at = Column(DateTime, server_default=func.now())
    completed_at = Column(DateTime)
    
    # 关系 (已移除强关联)
    # sharer = relationship("User", foreign_keys=[sharer_id])
    # invitee = relationship("User", foreign_keys=[invitee_id])
    
    # 索引
    __table_args__ = (
        Index("idx_sharer_id", "sharer_id"),
        Index("idx_status", "status"),
    )


class LeaderboardSnapshot(Base):
    """排行榜快照表"""
    __tablename__ = "leaderboard_snapshots"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    leaderboard_type = Column(Enum("artifact_collector", "city_guardian", "weekly_active", name="leaderboard_type"), nullable=False)
    rank = Column(Integer, nullable=False)
    score = Column(Integer, nullable=False)
    extra_data = Column(JSON, comment="额外数据，如图鉴进度、游戏时长等")
    snapshot_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_leaderboard_date", "leaderboard_type", "snapshot_date"),
        Index("idx_user_id", "user_id"),
        Index("idx_rank", "rank"),
    )


class AIInteraction(Base):
    """AI交互记录表"""
    __tablename__ = "ai_interactions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    interaction_type = Column(Enum("thief_diary", "boss_clue", "artifact_story", name="ai_interaction_type"), nullable=False)
    context_id = Column(String(100), comment="上下文ID，如文物ID、任务ID等")
    prompt = Column(Text, comment="发送给AI的提示")
    response = Column(Text, comment="AI的回复")
    created_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_interaction_type", "interaction_type"),
        Index("idx_created_at", "created_at"),
    )


