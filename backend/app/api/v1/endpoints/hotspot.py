"""
热点配置API - 已废弃
SceneHotspot已删除，热点数据现在从前端传入
"""
from fastapi import APIRouter
from typing import Dict, Any

router = APIRouter()


@router.get("/")
async def deprecated_notice() -> Dict[str, Any]:
    """废弃通知"""
    return {
        "message": "SceneHotspot functionality has been removed. Hotspot data is now passed from frontend.",
        "status": "deprecated"
    }


# 所有其他端点都已废弃，因为 SceneHotspot 模型已删除
# 热点数据现在完全从前端传入，不再需要后台配置功能