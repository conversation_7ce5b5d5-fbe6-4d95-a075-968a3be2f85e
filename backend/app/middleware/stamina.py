"""
体力管理中间件
统一处理体力检查、消耗和奖励计算逻辑
"""
from typing import Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class StaminaManager:
    """体力管理器 - 统一处理体力相关逻辑"""
    
    def __init__(self):
        # PRD配置的体力参数
        self.max_stamina = 120  # 最大体力
        self.low_stamina_threshold = 30  # 低体力阈值
        self.low_stamina_penalty = 0.75  # 低体力经验惩罚系数 (75%)
        self.hotspot_stamina_cost = 1  # 每次收集消耗体力
    
    async def check_stamina_requirements(
        self, 
        user_id: int,
        action: str = "collect_hotspot",
        stamina_cost: int = None
    ) -> Dict[str, Any]:
        """
        检查体力是否满足操作要求
        
        Args:
            user_id: 用户ID
            action: 操作类型 (collect_hotspot, etc.)
            stamina_cost: 消耗的体力值，如果为None则使用默认值
            
        Returns:
            {"can_proceed": bool, "current_stamina": int, "warning": str}
        """
        try:
            from app.services.experience_cache_service import experience_cache_service
            
            # 获取当前体力值
            stamina_stats = await experience_cache_service.get_user_cache_stats(user_id)
            current_stamina = stamina_stats.get("stamina", self.max_stamina)
            
            # 确定体力消耗
            if stamina_cost is None:
                stamina_cost = self._get_default_stamina_cost(action)
            
            # 检查是否有足够体力
            can_proceed = current_stamina >= stamina_cost
            
            result = {
                "can_proceed": can_proceed,
                "current_stamina": current_stamina,
                "stamina_cost": stamina_cost,
                "remaining_stamina": max(0, current_stamina - stamina_cost)
            }
            
            # 添加警告信息
            if not can_proceed:
                result["warning"] = f"体力不足，需要 {stamina_cost} 点体力，当前仅有 {current_stamina} 点"
            elif current_stamina < self.low_stamina_threshold:
                result["warning"] = f"体力较低 ({current_stamina}/{self.max_stamina})，经验获得将减少25%"
            
            return result
            
        except Exception as e:
            logger.error(f"检查体力需求失败: {e}")
            return {
                "can_proceed": False,
                "current_stamina": 0,
                "stamina_cost": stamina_cost or 0,
                "error": str(e)
            }
    
    def calculate_stamina_penalty(self, base_experience: int, current_stamina: int) -> Tuple[int, float]:
        """
        根据体力计算经验惩罚
        
        Args:
            base_experience: 基础经验值
            current_stamina: 当前体力值
            
        Returns:
            (final_experience, penalty_factor)
        """
        if current_stamina < self.low_stamina_threshold:
            penalty_factor = self.low_stamina_penalty
            final_experience = int(base_experience * penalty_factor)
            logger.info(f"低体力惩罚: {base_experience} -> {final_experience} (体力: {current_stamina})")
            return final_experience, penalty_factor
        
        return base_experience, 1.0
    
    async def consume_stamina(
        self,
        user_id: int,
        action: str = "collect_hotspot",
        stamina_cost: int = None,
        reason: str = None
    ) -> Dict[str, Any]:
        """
        消耗体力
        
        Args:
            user_id: 用户ID
            action: 操作类型
            stamina_cost: 消耗的体力值
            reason: 消耗原因
            
        Returns:
            体力更新结果
        """
        try:
            from app.services.experience_cache_service import experience_cache_service
            
            if stamina_cost is None:
                stamina_cost = self._get_default_stamina_cost(action)
            
            if reason is None:
                reason = f"执行{action}"
            
            return await experience_cache_service.update_stamina(
                user_id=user_id,
                stamina_change=-stamina_cost,
                reason=reason
            )
            
        except Exception as e:
            logger.error(f"消耗体力失败: {e}")
            return {"error": str(e)}
    
    def _get_default_stamina_cost(self, action: str) -> int:
        """获取默认体力消耗"""
        stamina_costs = {
            "collect_hotspot": 1,
            "answer_quiz": 2,
            "open_treasure": 1,
            "boss_battle": 5
        }
        return stamina_costs.get(action, 1)
    
    def get_stamina_info(self) -> Dict[str, Any]:
        """获取体力系统信息"""
        return {
            "max_stamina": self.max_stamina,
            "low_stamina_threshold": self.low_stamina_threshold,
            "low_stamina_penalty": self.low_stamina_penalty,
            "hotspot_stamina_cost": self.hotspot_stamina_cost
        }


# 全局体力管理器实例
stamina_manager = StaminaManager()